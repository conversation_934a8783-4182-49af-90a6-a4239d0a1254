# 本地大模型使用指南

## 📋 目录
1. [环境准备](#环境准备)
2. [模型配置](#模型配置)
3. [协作工作流程](#协作工作流程)
4. [单独运行Comprehensive Decision Agent](#单独运行comprehensive-decision-agent)
5. [统一工作流程使用](#统一工作流程使用)
6. [故障排除](#故障排除)

## 🔧 环境准备

### 1. 安装依赖
```bash
# 基础依赖
pip install torch transformers accelerate

# 量化支持 (推荐)
pip install bitsandbytes

# 向量数据库支持 (可选)
pip install faiss-cpu sentence-transformers

# 其他依赖
pip install numpy pandas scikit-learn
```

### 2. 模型文件准备
```bash
# 创建模型目录
mkdir -p ./models/llama3-7b

# 下载模型文件 (示例)
# 将您的llama3-7b模型文件放置在 ./models/llama3-7b/ 目录下
# 包括: config.json, tokenizer.json, pytorch_model.bin 等
```

### 3. 验证环境
```bash
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
```

## ⚙️ 模型配置

### 1. 基础配置文件 (model_config.json)
```json
{
  "model_type": "llama3-7b",
  "model_path": "./models/llama3-7b",
  "quantization": true,
  "max_length": 2048,
  "temperature": 0.7,
  "top_p": 0.9,
  "test_mode": false,
  "device": "auto"
}
```

### 2. 自定义配置
```python
from model_config import ModelConfig

# 创建自定义配置
config = ModelConfig(
    model_type="llama3-7b",
    model_path="./your_model_path",
    quantization=True,  # 启用4-bit量化节省内存
    max_length=4096,    # 增加最大长度
    temperature=0.6,    # 降低温度提高确定性
    device="cuda:0"     # 指定GPU设备
)

# 保存配置
config.save_to_file("custom_model_config.json")
```

### 3. 环境变量配置
```bash
export MODEL_PATH="./models/llama3-7b"
export MODEL_TYPE="llama3-7b"
export QUANTIZATION="true"
export TEMPERATURE="0.7"
export DEVICE="auto"
export TEST_MODE="false"
```

## 🤝 协作工作流程

### 1. 完整四阶段协作流程

```python
#!/usr/bin/env python3
"""
协作工作流程示例 - 使用本地大模型
"""

from model_config import ModelConfig, set_global_config
from unified_workflow_config import create_unified_workflow

def run_collaborative_workflow():
    """运行完整的协作工作流程"""
    
    # 1. 配置本地大模型
    model_config = ModelConfig(
        model_path="./models/llama3-7b",
        quantization=True,
        test_mode=False  # 使用真实模型
    )
    set_global_config(model_config)
    
    # 2. 创建统一工作流程管理器
    workflow_manager = create_unified_workflow()
    
    # 3. 运行协作流程
    print("🚀 启动协作工作流程...")
    
    # 假设您已经有了Specific Advice Agent的输出
    specific_advice_output = "specific_advice_results.json"
    
    # 运行协作流程
    result_path = workflow_manager.run_collaborative_workflow(specific_advice_output)
    
    print(f"✅ 协作流程完成，结果保存在: {result_path}")
    
    return result_path

if __name__ == "__main__":
    run_collaborative_workflow()
```

### 2. 分步骤运行协作流程

```python
#!/usr/bin/env python3
"""
分步骤协作流程 - 逐个运行每个Agent
"""

import importlib.util
from model_config import ModelConfig, set_global_config

def run_step_by_step_workflow():
    """分步骤运行协作工作流程"""
    
    # 配置模型
    model_config = ModelConfig(
        model_path="./models/llama3-7b",
        quantization=True,
        test_mode=False
    )
    set_global_config(model_config)
    
    # 1. Summarization Agent
    print("📊 步骤1: 运行Summarization Agent...")
    spec = importlib.util.spec_from_file_location("summarization_agent", "Summarization Agent.py")
    summarization_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(summarization_module)
    
    summarization_agent = summarization_module.SummarizationAgent()
    summary_result = summarization_agent.run("input_data.json")
    print(f"   ✅ 摘要结果: {summary_result}")
    
    # 2. Prompt Agent
    print("💡 步骤2: 运行Prompt Agent...")
    spec = importlib.util.spec_from_file_location("prompt_agent", "Prompt Agent.py")
    prompt_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(prompt_module)
    
    prompt_agent = prompt_module.PromptAgent()
    prompt_result = prompt_agent.run(summary_result)
    print(f"   ✅ 提示结果: {prompt_result}")
    
    # 3. Specific Advice Agent
    print("🎯 步骤3: 运行Specific Advice Agent...")
    spec = importlib.util.spec_from_file_location("specific_advice_agent", "Specific Advice Agent.py")
    advice_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(advice_module)
    
    advice_agent = advice_module.SpecificAdviceAgent()
    advice_result = advice_agent.run(prompt_result)
    print(f"   ✅ 建议结果: {advice_result}")
    
    # 4. Comprehensive Decision Agent (使用统一工作流程)
    print("🎯 步骤4: 运行Comprehensive Decision Agent...")
    from unified_workflow_config import create_unified_workflow
    
    workflow_manager = create_unified_workflow()
    final_result = workflow_manager.run_collaborative_workflow(advice_result)
    print(f"   ✅ 最终决策结果: {final_result}")
    
    return final_result

if __name__ == "__main__":
    run_step_by_step_workflow()
```

## 🔧 单独运行Comprehensive Decision Agent

### 1. 使用CVSS数据单独运行

```python
#!/usr/bin/env python3
"""
单独运行Comprehensive Decision Agent - CVSS数据
"""

from model_config import ModelConfig, set_global_config
from unified_workflow_config import create_unified_workflow

def run_standalone_with_cvss():
    """使用CVSS数据单独运行"""
    
    # 1. 配置本地大模型
    model_config = ModelConfig(
        model_path="./models/llama3-7b",
        quantization=True,
        temperature=0.6,  # 降低温度提高决策一致性
        test_mode=False
    )
    set_global_config(model_config)
    
    # 2. 创建工作流程管理器
    workflow_manager = create_unified_workflow()
    
    # 3. 使用CVSS数据运行
    print("🔧 单独运行Comprehensive Decision Agent (CVSS数据)...")
    
    cvss_data_file = "output_0525_finetune_metrics.json"
    result_path = workflow_manager.run_standalone_workflow(cvss_data_file)
    
    print(f"✅ 单独运行完成，结果保存在: {result_path}")
    
    return result_path

if __name__ == "__main__":
    run_standalone_with_cvss()
```

### 2. 使用策略训练数据单独运行

```python
#!/usr/bin/env python3
"""
单独运行Comprehensive Decision Agent - 策略训练数据
"""

from model_config import ModelConfig, set_global_config
from unified_workflow_config import create_unified_workflow

def run_standalone_with_strategy_data():
    """使用策略训练数据单独运行"""
    
    # 配置模型
    model_config = ModelConfig(
        model_path="./models/llama3-7b",
        quantization=True,
        max_length=4096,  # 增加长度处理复杂策略
        test_mode=False
    )
    set_global_config(model_config)
    
    # 运行单独工作流程
    workflow_manager = create_unified_workflow()
    
    print("🔧 单独运行Comprehensive Decision Agent (策略训练数据)...")
    
    strategy_data_file = "output_1112_strategy_train_data.json"
    result_path = workflow_manager.run_standalone_workflow(strategy_data_file)
    
    print(f"✅ 单独运行完成，结果保存在: {result_path}")
    
    return result_path

if __name__ == "__main__":
    run_standalone_with_strategy_data()
```

### 3. 直接使用Comprehensive Decision Agent

```python
#!/usr/bin/env python3
"""
直接使用Comprehensive Decision Agent
"""

import importlib.util
from model_config import ModelConfig, set_global_config

def run_comprehensive_agent_directly():
    """直接运行Comprehensive Decision Agent"""
    
    # 配置模型
    model_config = ModelConfig(
        model_path="./models/llama3-7b",
        quantization=True,
        test_mode=False
    )
    set_global_config(model_config)
    
    # 导入Comprehensive Decision Agent
    spec = importlib.util.spec_from_file_location(
        "comprehensive_decision_agent", 
        "Comprehensive Decision Agent.py"
    )
    comprehensive_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(comprehensive_module)
    
    # 创建Agent配置
    agent_config = comprehensive_module.ComprehensiveDecisionConfig(
        output_dir="./direct_comprehensive_outputs",
        max_strategies=10,
        enable_explanation=True,
        decision_threshold=0.5
    )
    
    # 创建Agent实例
    agent = comprehensive_module.ComprehensiveDecisionAgent(agent_config)
    
    # 运行Agent
    print("🎯 直接运行Comprehensive Decision Agent...")
    
    input_file = "your_input_data.json"  # 任何支持的格式
    result_path = agent.run(input_file, "direct_result.json")
    
    print(f"✅ 直接运行完成，结果保存在: {result_path}")
    
    return result_path

if __name__ == "__main__":
    run_comprehensive_agent_directly()
```

## 🔄 统一工作流程使用

### 1. 快速使用

```python
from unified_workflow_config import quick_process

# 快速处理任何格式的输入文件
result_path = quick_process("input_data.json", "output_result.json")
print(f"处理完成: {result_path}")
```

### 2. 自定义配置使用

```python
from unified_workflow_config import UnifiedWorkflowConfig, UnifiedWorkflowManager
from model_config import ModelConfig, set_global_config

# 1. 配置本地大模型
model_config = ModelConfig(
    model_path="./models/llama3-7b",
    quantization=True,
    temperature=0.7,
    test_mode=False
)
set_global_config(model_config)

# 2. 创建统一工作流程配置
workflow_config = UnifiedWorkflowConfig(
    unified_data_output_dir="./custom_outputs",
    enable_auto_format_detection=True,
    comprehensive_agent_config={
        "output_dir": "./custom_outputs",
        "max_strategies": 15,
        "enable_explanation": True,
        "decision_threshold": 0.6
    }
)

# 3. 创建工作流程管理器
manager = UnifiedWorkflowManager(workflow_config)

# 4. 运行不同模式
# 协作模式
collaborative_result = manager.run_collaborative_workflow("specific_advice_output.json")

# 单独运行模式
standalone_result = manager.run_standalone_workflow("cvss_data.json")

print(f"协作结果: {collaborative_result}")
print(f"单独运行结果: {standalone_result}")
```

## 🛠️ 故障排除

### 1. 内存不足
```python
# 启用量化和梯度检查点
model_config = ModelConfig(
    quantization=True,  # 4-bit量化
    max_length=2048,    # 减少最大长度
    device="cpu"        # 使用CPU如果GPU内存不足
)
```

### 2. 模型加载失败
```bash
# 检查模型文件
ls -la ./models/llama3-7b/

# 验证模型格式
python -c "from transformers import AutoTokenizer; AutoTokenizer.from_pretrained('./models/llama3-7b')"
```

### 3. 性能优化
```python
# 优化配置
model_config = ModelConfig(
    quantization=True,      # 启用量化
    device="cuda:0",        # 指定GPU
    temperature=0.6,        # 降低温度
    max_length=2048        # 合适的长度
)
```

### 4. 测试模式回退
```python
# 如果模型有问题，可以先用测试模式验证流程
model_config = ModelConfig(
    test_mode=True  # 使用模拟响应
)
```

## 📊 性能监控

```python
import time
import psutil
import torch

def monitor_performance():
    """监控性能指标"""
    
    start_time = time.time()
    start_memory = psutil.virtual_memory().used / 1024**3  # GB
    
    if torch.cuda.is_available():
        start_gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
    
    # 运行您的工作流程
    result = your_workflow_function()
    
    end_time = time.time()
    end_memory = psutil.virtual_memory().used / 1024**3
    
    print(f"执行时间: {end_time - start_time:.2f}秒")
    print(f"内存使用: {end_memory - start_memory:.2f}GB")
    
    if torch.cuda.is_available():
        end_gpu_memory = torch.cuda.memory_allocated() / 1024**3
        print(f"GPU内存使用: {end_gpu_memory - start_gpu_memory:.2f}GB")
    
    return result
```

## 🎯 最佳实践

1. **内存管理**: 启用量化，合理设置max_length
2. **设备选择**: 优先使用GPU，内存不足时回退到CPU
3. **温度设置**: 决策任务使用较低温度(0.6-0.7)
4. **批处理**: 大量数据时考虑分批处理
5. **监控**: 监控内存和GPU使用情况
6. **测试**: 先用测试模式验证流程，再切换到真实模型
